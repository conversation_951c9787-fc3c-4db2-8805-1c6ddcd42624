console.log("📦 إضافة نور: بدء مراقبة الصفحة بعد الضغط على بحث...");

let allStudents = [];
let currentPage = 1;
let totalPages = 1;
let isExtracting = false;

function extractStudentsFromTable() {
  // البحث عن الجدول بطرق متعددة
  let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (!table) {
    // محاولة البحث بطرق أخرى
    table = document.querySelector("table[id*='gvUserInfo']");
  }

  if (!table) {
    table = document.querySelector("table.GridViewStyle");
  }

  if (!table) {
    // البحث عن أي جدول يحتوي على بيانات الطلاب
    const tables = document.querySelectorAll("table");
    for (const t of tables) {
      const headers = t.querySelectorAll("th");
      if (headers.length > 5) {
        table = t;
        break;
      }
    }
  }

  if (!table) {
    console.warn("❌ لم يتم العثور على جدول البيانات");
    return;
  }

  console.log("✅ تم العثور على الجدول:", table.id || table.className);

  const rows = table.querySelectorAll("tr") || [];
  let count = 0;

  rows.forEach((row) => {
    const cols = row.querySelectorAll("td");
    if (cols.length >= 5) { // تقليل الحد الأدنى للأعمدة
      const studentData = {
        username: cols[0]?.innerText?.trim() || '',
        name: cols[1]?.innerText?.trim() || '',
        national_id: cols[3]?.innerText?.trim() || cols[2]?.innerText?.trim() || '',
        grade: cols[5]?.innerText?.trim() || cols[4]?.innerText?.trim() || '',
        class: cols[7]?.innerText?.trim() || cols[6]?.innerText?.trim() || cols[5]?.innerText?.trim() || ''
      };

      // تجاهل الصفوف الفارغة أو صفوف العناوين
      if (studentData.name && studentData.name !== 'الاسم' && studentData.name.length > 2) {
        allStudents.push(studentData);
        count++;
      }
    }
  });

  console.log(`✅ تم استخراج ${count} طالبًا من الصفحة ${currentPage}`);
}

function getTotalPages() {
  const pager = document.querySelector(".MyPagerStyle");
  if (!pager) return 1;
  const pageLinks = pager.querySelectorAll("a[href*='Page$']");
  const lastLink = Array.from(pageLinks).pop();
  const match = lastLink?.href?.match(/Page\$(\d+)/);
  return match ? parseInt(match[1]) : 1;
}

function triggerPostBack(target, arg) {
  // استخدام الطريقة الآمنة بدلاً من inline script
  if (typeof __doPostBack === 'function') {
    __doPostBack(target, arg);
  } else {
    console.warn('⚠️ __doPostBack غير متوفر');
  }
}

function goToPage(pageNumber) {
  const pager = document.querySelector(".MyPagerStyle");
  const pageLinks = pager?.querySelectorAll("a[href*='Page$']") || [];
  const link = Array.from(pageLinks).find(a => a.href.includes(`Page$${pageNumber}`));

  if (link) {
    // استخدام click() بدلاً من JavaScript URL
    console.log(`🔗 الانتقال إلى الصفحة ${pageNumber} باستخدام click()`);
    link.click();
    return true;
  } else {
    // محاولة استخدام __doPostBack إذا كان متوفراً
    const target = `ctl00$PlaceHolderMain$gvUserInfo`;
    const arg = `Page$${pageNumber}`;
    console.log(`� محاولة الانتقال باستخدام __doPostBack إلى الصفحة ${pageNumber}`);
    triggerPostBack(target, arg);
    return true;
  }
}
function waitForTableAndContinue(retry = 0) {
  // البحث عن الجدول بطرق متعددة
  let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (!table) {
    table = document.querySelector("table[id*='gvUserInfo']");
  }

  if (!table) {
    table = document.querySelector("table.GridViewStyle");
  }

  if (!table) {
    // البحث عن أي جدول يحتوي على بيانات
    const tables = document.querySelectorAll("table");
    for (const t of tables) {
      const rows = t.querySelectorAll("tr");
      if (rows.length > 1) {
        const firstDataRow = rows[1];
        const cols = firstDataRow?.querySelectorAll("td");
        if (cols && cols.length >= 5) {
          table = t;
          console.log("🔍 تم العثور على جدول بديل:", t.className || t.id);
          break;
        }
      }
    }
  }

  console.log(`🔍 محاولة ${retry + 1}: البحث عن الجدول...`);

  if (table && table.rows.length > 1) {
    console.log(`✅ تم العثور على الجدول مع ${table.rows.length} صف`);
    extractStudentsFromTable();
    currentPage++;
    if (currentPage <= totalPages) {
      setTimeout(() => {
        console.log(`📄 الانتقال إلى الصفحة ${currentPage}...`);
        goToPage(currentPage);
        setTimeout(() => waitForTableAndContinue(), 3000);
      }, 1000);
    } else {
      console.log(`✅ اكتمل استخراج ${allStudents.length} طالبًا من ${totalPages} صفحة`);

      // إضافة معلومات إضافية للبيانات المحفوظة
      const dataToSave = {
        studentsData: allStudents,
        extractionInfo: {
          totalStudents: allStudents.length,
          totalPages: totalPages,
          extractionDate: new Date().toISOString(),
          extractionTime: new Date().toLocaleString('ar-SA'),
          source: 'نظام نور - إدارة المستخدمين'
        }
      };

      console.log("💾 البيانات التي سيتم حفظها:", dataToSave);

      chrome.storage.local.set(dataToSave, () => {
        if (chrome.runtime.lastError) {
          console.error("❌ خطأ في حفظ البيانات:", chrome.runtime.lastError);
          return;
        }

        console.log("💾 تم حفظ جميع بيانات الطلاب في التخزين المحلي");
        console.log(`📊 الإحصائيات: ${allStudents.length} طالب من ${totalPages} صفحة`);

        // التحقق من الحفظ
        chrome.storage.local.get(['studentsData'], (result) => {
          if (result.studentsData && result.studentsData.length > 0) {
            console.log("✅ تم التحقق من حفظ البيانات بنجاح");
          } else {
            console.error("❌ فشل في التحقق من حفظ البيانات");
          }
        });

        // إشعار المستخدم بالانتهاء
        if (typeof alert !== 'undefined') {
          alert(`✅ تم استخراج ${allStudents.length} طالبًا بنجاح!\n\nيمكنك الآن فتح نافذة الإضافة لعرض النتائج.`);
        }

        // إعادة تعيين حالة الاستخراج
        isExtracting = false;
      });
    }
  } else {
    if (retry < 15) { // زيادة عدد المحاولات
      console.log(`⏳ لم يتم العثور على الجدول، المحاولة ${retry + 1}/15...`);
      setTimeout(() => waitForTableAndContinue(retry + 1), 2000); // زيادة وقت الانتظار
    } else {
      console.warn("⚠️ لم يظهر الجدول في الوقت المحدد.");
      console.log("🔍 الجداول الموجودة في الصفحة:");
      document.querySelectorAll("table").forEach((t, i) => {
        console.log(`جدول ${i + 1}:`, t.id || t.className, `عدد الصفوف: ${t.rows.length}`);
      });

      // إعادة تعيين حالة الاستخراج
      isExtracting = false;
    }
  }
}

// بدء التنفيذ
setTimeout(() => {
  if (isExtracting) {
    console.log("⚠️ عملية الاستخراج جارية بالفعل...");
    return;
  }

  isExtracting = true;
  console.log("🔍 محاولة العثور على جدول الطلاب...");
  totalPages = getTotalPages();
  console.log(`📄 عدد الصفحات الكلي: ${totalPages}`);
  waitForTableAndContinue();
}, 2000);
