console.log("📦 إضافة نور: بدء مراقبة الصفحة بعد الضغط على بحث...");

let allStudents = [];
let currentPage = 1;
let totalPages = 1;

function extractStudentsFromTable() {
  const table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
  const rows = table?.querySelectorAll("tr") || [];
  let count = 0;

  rows.forEach(row => {
    const cols = row.querySelectorAll("td");
    if (cols.length >= 8) {
      allStudents.push({
        username: cols[0].innerText.trim(),
        name: cols[1].innerText.trim(),
        national_id: cols[3].innerText.trim(),
        grade: cols[5].innerText.trim(),
        class: cols[7].innerText.trim()
      });
      count++;
    }
  });

  console.log(`✅ تم استخراج ${count} طالبًا من الصفحة ${currentPage}`);
}

function getTotalPages() {
  const pager = document.querySelector(".MyPagerStyle");
  if (!pager) return 1;
  const pageLinks = pager.querySelectorAll("a[href*='Page$']");
  const lastLink = Array.from(pageLinks).pop();
  const match = lastLink?.href?.match(/Page\$(\d+)/);
  return match ? parseInt(match[1]) : 1;
}

function triggerPostBack(target, arg) {
  const script = document.createElement("script");
  script.textContent = `__doPostBack('${target}', '${arg}');`;
  document.documentElement.appendChild(script);
  script.remove();
}

function goToPage(pageNumber) {
    const pager = document.querySelector(".MyPagerStyle");
    const pageLinks = pager?.querySelectorAll("a[href*='Page$']") || [];
    const link = Array.from(pageLinks).find(a => a.href.includes(`Page$${pageNumber}`));
    if (link) {
      link.click(); // ✅ يعمل بدون inline script
    } else {
      console.warn(`🔴 لم يتم العثور على رابط الصفحة ${pageNumber}`);
    }
  }
function waitForTableAndContinue(retry = 0) {
  const table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (table && table.rows.length > 1) {
    extractStudentsFromTable();
    currentPage++;
    if (currentPage <= totalPages) {
      setTimeout(() => {
        console.log(`📄 الانتقال إلى الصفحة ${currentPage}...`);
        goToPage(currentPage);
        setTimeout(() => waitForTableAndContinue(), 3000);
      }, 1000);
    } else {
      console.log(`✅ اكتمل استخراج ${allStudents.length} طالبًا من ${totalPages} صفحة`);
      chrome.storage.local.set({ studentsData: allStudents }, () => {
        console.log("💾 تم حفظ جميع بيانات الطلاب في التخزين المحلي");
      });
    }
  } else {
    if (retry < 10) {
      setTimeout(() => waitForTableAndContinue(retry + 1), 1000);
    } else {
      console.warn("⚠️ لم يظهر الجدول في الوقت المحدد.");
    }
  }
}

// بدء التنفيذ
setTimeout(() => {
  console.log("🔍 محاولة العثور على جدول الطلاب...");
  totalPages = getTotalPages();
  console.log(`📄 عدد الصفحات الكلي: ${totalPages}`);
  waitForTableAndContinue();
}, 2000);
