console.log("📦 إضافة نور: بدء مراقبة الصفحة بعد الضغط على بحث...");

let allStudents = [];
let currentPage = 1;
let totalPages = 1;
let isExtracting = false;
let detailsQueue = []; // قائمة انتظار لجلب التفاصيل
let isProcessingDetails = false;

// وظيفة جلب تفاصيل الطالب الكاملة
async function getStudentDetails(username, linkInfo) {
  return new Promise((resolve) => {
    console.log(`🔍 جلب تفاصيل الطالب: ${username}`);
    console.log(`🔗 معلومات الرابط:`, linkInfo);

    // البحث عن الرابط في الصفحة الحالية
    const currentLink = document.querySelector(`a[id="${linkInfo.id}"]`) ||
                       Array.from(document.querySelectorAll('a')).find(a => a.textContent.trim() === username);

    if (!currentLink) {
      console.error(`❌ لم يتم العثور على رابط الطالب ${username} في الصفحة الحالية`);
      resolve({});
      return;
    }

    console.log(`✅ تم العثور على رابط الطالب: ${currentLink.href}`);

    // استخراج معلومات PostBack من الرابط
    const href = currentLink.getAttribute('href') || linkInfo.href;
    if (href && href.includes('__doPostBack')) {
      // استخراج معاملات PostBack
      const match = href.match(/__doPostBack\('([^']+)','([^']*)'\)/);
      if (match) {
        const target = match[1];
        const argument = match[2];

        console.log(`🔗 تنفيذ PostBack: target=${target}, argument=${argument}`);

        // تنفيذ PostBack
        if (typeof __doPostBack === 'function') {
          __doPostBack(target, argument);

          // انتظار تحميل صفحة التفاصيل
          setTimeout(() => {
            try {
              // استخراج التفاصيل من الصفحة
              const details = extractDetailsFromPage();

              // العودة للصفحة السابقة
              window.history.back();

              // انتظار العودة للصفحة الأصلية
              setTimeout(() => {
                resolve(details);
              }, 3000);

            } catch (error) {
              console.error(`❌ خطأ في جلب تفاصيل ${username}:`, error);
              window.history.back();
              setTimeout(() => resolve({}), 3000);
            }
          }, 4000);
        } else {
          console.error("❌ __doPostBack غير متوفر");
          resolve({});
        }
      } else {
        console.error(`❌ لا يمكن استخراج معاملات PostBack من: ${href}`);
        resolve({});
      }
    } else {
      console.error(`❌ رابط غير صحيح: ${href}`);
      resolve({});
    }
  });
}

// استخراج التفاصيل من صفحة تفاصيل الطالب
function extractDetailsFromPage() {
  const details = {};

  // البحث عن جدول التفاصيل
  const detailsTable = document.querySelector('table');
  if (!detailsTable) return details;

  const rows = detailsTable.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length >= 2) {
      const label = cells[0]?.textContent?.trim();
      const value = cells[1]?.textContent?.trim();

      if (label && value) {
        // تحويل التسميات العربية إلى مفاتيح إنجليزية
        const fieldMap = {
          'الاسم الكامل': 'full_name',
          'اسم المستخدم': 'username',
          'رقم الهوية': 'national_id',
          'تاريخ الميلاد': 'birth_date',
          'الإدارة التعليمية': 'education_department',
          'المدرسة': 'school',
          'الصف': 'grade',
          'الفصل': 'class',
          'حالة الطالب': 'student_status',
          'مستمر في الدراسة': 'continuing_study',
          'الثاني المتوسط': 'grade_level',
          'منتظم': 'study_system',
          'الجنسية': 'nationality',
          'هاتف المنزل': 'home_phone',
          'الجوال': 'mobile',
          'البريد الإلكتروني': 'email',
          'صندوق البريد': 'po_box'
        };

        const fieldKey = fieldMap[label] || label.replace(/\s+/g, '_').toLowerCase();
        details[fieldKey] = value;
      }
    }
  });

  console.log(`✅ تم استخراج تفاصيل الطالب:`, details);
  return details;
}

function extractStudentsFromTable() {
  // البحث عن الجدول بطرق متعددة
  let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (!table) {
    table = document.querySelector("table[id*='gvUserInfo']");
  }

  if (!table) {
    table = document.querySelector("table.GridClass");
  }

  if (!table) {
    // البحث عن أي جدول يحتوي على بيانات الطلاب
    const tables = document.querySelectorAll("table");
    for (const t of tables) {
      if (t.rows.length > 1) {
        const firstDataRow = t.rows[1];
        const cells = firstDataRow?.cells;
        if (cells && cells.length >= 8) {
          const cellTexts = Array.from(cells).map(cell => cell.textContent.trim());
          const hasStudentData = cellTexts.some(text =>
            /^\d{10}$/.test(text) ||
            /الصف|المتوسط|الثانوي|الابتدائي/.test(text) ||
            /^[a-zA-Z0-9]+$/.test(text) && text.length > 3
          );
          if (hasStudentData) {
            table = t;
            break;
          }
        }
      }
    }
  }

  if (!table) {
    console.warn("❌ لم يتم العثور على جدول البيانات في extractStudentsFromTable");
    return;
  }

  console.log(`✅ استخراج البيانات من الجدول في الصفحة ${currentPage}:`, table.id || table.className);

  const rows = table.querySelectorAll("tr") || [];
  let count = 0;
  let pageStudents = []; // مصفوفة مؤقتة للطلاب في هذه الصفحة

  // تشخيص محتوى الصفحة الحالية
  console.log(`🔍 تشخيص الصفحة ${currentPage}:`);
  console.log(`📊 عدد الصفوف في الجدول: ${rows.length}`);

  // عرض عينة من أول 3 طلاب للتشخيص
  let sampleCount = 0;

  rows.forEach((row) => {
    const cols = row.querySelectorAll("td");

    // تجاهل صف التنقل بين الصفحات (MyPagerStyle)
    if (row.classList.contains('MyPagerStyle')) {
      return;
    }

    // مرونة في عدد الأعمدة - قبول 8 أعمدة أو أكثر
    if (cols.length >= 8) {
      // استخراج اسم المستخدم من الرابط في العمود الأول
      const usernameLink = cols[0].querySelector('a');
      const username = usernameLink ? usernameLink.textContent.trim() : cols[0].textContent.trim();

      // عرض عينة للتشخيص
      if (sampleCount < 3) {
        const name = cols[1]?.textContent?.trim() || '';
        const national_id = cols[3]?.textContent?.trim() || '';
        console.log(`📋 عينة ${sampleCount + 1}: ${username} - ${name} - ${national_id}`);
        sampleCount++;
      }

      // إضافة الطالب لقائمة انتظار جلب التفاصيل
      if (usernameLink) {
        // حفظ معلومات الرابط
        const linkInfo = {
          username,
          href: usernameLink.getAttribute('href'),
          id: usernameLink.id,
          onclick: usernameLink.getAttribute('onclick')
        };
        detailsQueue.push(linkInfo);
        console.log(`📝 تم إضافة ${username} لقائمة جلب التفاصيل`);
      }

      // تحديد البيانات بناءً على عدد الأعمدة
      let studentData;

      if (cols.length >= 11) {
        // الجدول الكامل مع 11 عمود
        studentData = {
          username: username,
          name: cols[1]?.textContent?.trim() || '',
          name_english: cols[2]?.textContent?.trim() || '',
          national_id: cols[3]?.textContent?.trim() || '',
          study_system: cols[4]?.textContent?.trim() || '',
          grade: cols[5]?.textContent?.trim() || '',
          department: cols[6]?.textContent?.trim() || '',
          class: cols[7]?.textContent?.trim() || '',
          enrollment_status: cols[8]?.textContent?.trim() || '',
          verification: cols[9]?.textContent?.trim() || '',
          record_status: cols[10]?.textContent?.trim() || ''
        };
      } else {
        // جدول مبسط مع 8 أعمدة أو أكثر
        studentData = {
          username: username,
          name: cols[1]?.textContent?.trim() || '',
          name_english: cols[2]?.textContent?.trim() || '',
          national_id: cols[3]?.textContent?.trim() || '',
          study_system: cols[4]?.textContent?.trim() || '',
          grade: cols[5]?.textContent?.trim() || '',
          department: cols[6]?.textContent?.trim() || '',
          class: cols[7]?.textContent?.trim() || '',
          enrollment_status: cols[8]?.textContent?.trim() || '',
          verification: cols[9]?.textContent?.trim() || '',
          record_status: cols[10]?.textContent?.trim() || ''
        };
      }

      // تجاهل الصفوف الفارغة أو صفوف العناوين
      if (studentData.name &&
          !['الاسم الرباعي', 'الاسم', 'Name'].includes(studentData.name) &&
          studentData.name.length > 2 &&
          studentData.national_id &&
          !['رقم السجل المدني', 'رقم الهوية', 'ID'].includes(studentData.national_id) &&
          studentData.national_id.length >= 8) {

        // تحقق من عدم وجود الطالب مسبقاً (تجنب التكرار)
        const isDuplicate = allStudents.some(existing =>
          existing.national_id === studentData.national_id ||
          existing.username === studentData.username
        );

        if (!isDuplicate) {
          pageStudents.push(studentData);
          count++;
          console.log(`📝 طالب ${allStudents.length + count}:`, studentData.name, `- ID: ${studentData.national_id}`);
        } else {
          console.log(`⚠️ طالب مكرر تم تجاهله:`, studentData.name, `- ID: ${studentData.national_id}`);
        }
      }
    }
  });

  // إضافة طلاب هذه الصفحة إلى المجموع الكلي
  allStudents.push(...pageStudents);

  console.log(`✅ تم استخراج ${count} طالبًا جديدًا من الصفحة ${currentPage} (إجمالي: ${allStudents.length})`);

  // إذا لم يتم استخراج أي طلاب جدد، قد تكون هناك مشكلة في التنقل
  if (count === 0 && currentPage > 1) {
    console.warn(`⚠️ لم يتم استخراج أي طلاب جدد من الصفحة ${currentPage} - قد تكون مشكلة في التنقل`);
    console.log(`🔍 تشخيص: الصفحة الحالية تحتوي على نفس البيانات من الصفحة السابقة`);
  }
}

// معالجة قائمة انتظار جلب التفاصيل
async function processDetailsQueue() {
  if (isProcessingDetails || detailsQueue.length === 0) return;

  isProcessingDetails = true;
  console.log(`🔄 بدء جلب تفاصيل ${detailsQueue.length} طالب...`);

  for (let i = 0; i < detailsQueue.length; i++) {
    const linkInfo = detailsQueue[i];
    const { username } = linkInfo;

    try {
      console.log(`📝 جلب تفاصيل الطالب ${i + 1}/${detailsQueue.length}: ${username}`);

      // جلب التفاصيل
      const details = await getStudentDetails(username, linkInfo);

      // البحث عن الطالب في القائمة وتحديث بياناته
      const studentIndex = allStudents.findIndex(s => s.username === username);
      if (studentIndex !== -1) {
        allStudents[studentIndex] = { ...allStudents[studentIndex], ...details };
        console.log(`✅ تم تحديث بيانات الطالب: ${username}`);
      }

      // انتظار قصير بين كل طالب
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.error(`❌ خطأ في جلب تفاصيل ${username}:`, error);
    }
  }

  // مسح قائمة الانتظار
  detailsQueue = [];
  isProcessingDetails = false;

  console.log(`✅ اكتمل جلب تفاصيل جميع الطلاب`);

  // حفظ البيانات المحدثة
  saveStudentsData();
}

function getTotalPages() {
  // البحث عن صف التنقل
  const pagerRow = document.querySelector("tr.MyPagerStyle");
  if (!pagerRow) {
    console.log("❌ لم يتم العثور على صف التنقل");
    return 1;
  }

  // البحث عن جميع الروابط في صف التنقل أولاً
  const allLinks = pagerRow.querySelectorAll("a");
  console.log(`🔍 إجمالي الروابط في صف التنقل: ${allLinks.length}`);

  // طباعة جميع الروابط للتشخيص
  allLinks.forEach((link, index) => {
    const href = link.getAttribute('href');
    const text = link.textContent.trim();
    console.log(`رابط ${index + 1}: "${text}" - href: ${href}`);
  });

  // البحث عن روابط الصفحات
  const pageLinks = pagerRow.querySelectorAll("a[href*='Page$']");
  console.log(`🔍 تم العثور على ${pageLinks.length} رابط صفحة`);

  if (pageLinks.length === 0) {
    console.log("❌ لا توجد روابط صفحات - صفحة واحدة فقط");
    return 1;
  }

  // العثور على أعلى رقم صفحة
  let maxPage = 1;
  pageLinks.forEach(link => {
    const href = link.getAttribute('href');
    const match = href.match(/Page\$(\d+)/);
    if (match) {
      const pageNum = parseInt(match[1]);
      if (pageNum > maxPage) {
        maxPage = pageNum;
      }
      console.log(`📄 تم العثور على صفحة رقم: ${pageNum}`);
    }
  });

  console.log(`📄 إجمالي عدد الصفحات: ${maxPage}`);
  return maxPage;
}

function triggerPostBack(target, arg) {
  // استخدام الطريقة الآمنة بدلاً من inline script
  if (typeof __doPostBack === 'function') {
    __doPostBack(target, arg);
  } else {
    console.warn('⚠️ __doPostBack غير متوفر');
  }
}

function goToPage(pageNumber) {
  // البحث عن صف التنقل
  const pagerRow = document.querySelector("tr.MyPagerStyle");
  if (!pagerRow) {
    console.warn("❌ لم يتم العثور على صف التنقل");
    return false;
  }

  // البحث عن جميع الروابط في صف التنقل
  const pageLinks = pagerRow.querySelectorAll("a");
  console.log(`🔍 عدد روابط التنقل: ${pageLinks.length}`);

  // طباعة جميع الروابط للتشخيص
  pageLinks.forEach((link, index) => {
    const href = link.getAttribute('href');
    const text = link.textContent.trim();
    console.log(`رابط ${index + 1}: "${text}" - href: ${href}`);
  });

  // البحث عن رابط الصفحة المحددة
  const link = Array.from(pageLinks).find(a => {
    const href = a.getAttribute('href');
    return href && href.includes(`Page$${pageNumber}`);
  });

  if (link) {
    console.log(`✅ تم العثور على رابط الصفحة ${pageNumber}`);

    // جرب الطريقتين: click() و __doPostBack
    try {
      // الطريقة الأولى: استخراج PostBack من الرابط وتنفيذه
      const href = link.getAttribute('href');
      if (href && href.includes('__doPostBack')) {
        const match = href.match(/__doPostBack\('([^']+)','([^']*)'\)/);
        if (match && typeof __doPostBack === 'function') {
          const target = match[1];
          const argument = match[2];
          console.log(`🔗 تنفيذ PostBack: __doPostBack('${target}', '${argument}')`);
          __doPostBack(target, argument);
          return true;
        }
      }

      // الطريقة الثانية: click مباشر
      console.log(`🔗 الانتقال إلى الصفحة ${pageNumber} باستخدام click()`);
      link.click();
      return true;

    } catch (error) {
      console.error(`❌ خطأ في الانتقال للصفحة ${pageNumber}:`, error);
      return false;
    }
  } else {
    // محاولة استخدام __doPostBack مباشرة
    const target = `ctl00$PlaceHolderMain$gvUserInfo`;
    const arg = `Page$${pageNumber}`;
    console.log(`🔄 محاولة الانتقال باستخدام __doPostBack مباشرة: ('${target}', '${arg}')`);

    if (typeof __doPostBack === 'function') {
      __doPostBack(target, arg);
      return true;
    } else {
      console.warn("❌ __doPostBack غير متوفر");
      return false;
    }
  }
}
function waitForTableAndContinue(retry = 0) {
  console.log(`🔍 محاولة ${retry + 1}: البحث عن الجدول...`);

  // البحث عن الجدول بطرق متعددة
  let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (!table) {
    table = document.querySelector("table[id*='gvUserInfo']");
  }

  if (!table) {
    table = document.querySelector("table.GridClass");
  }

  if (!table) {
    // البحث عن أي جدول يحتوي على بيانات طلاب محتملة
    const tables = document.querySelectorAll("table");
    for (const t of tables) {
      if (t.rows.length > 1) {
        const firstDataRow = t.rows[1];
        const cells = firstDataRow?.cells;

        // تحقق من وجود أعمدة كافية وبيانات تشبه بيانات الطلاب
        if (cells && cells.length >= 8) {
          const cellTexts = Array.from(cells).map(cell => cell.textContent.trim());

          // البحث عن أنماط تدل على بيانات طلاب (أرقام هوية، أسماء، صفوف)
          const hasStudentData = cellTexts.some(text =>
            /^\d{10}$/.test(text) || // رقم هوية (10 أرقام)
            /الصف|المتوسط|الثانوي|الابتدائي/.test(text) || // أسماء صفوف
            /^[a-zA-Z0-9]+$/.test(text) && text.length > 3 // اسم مستخدم محتمل
          );

          if (hasStudentData) {
            console.log("🔍 تم العثور على جدول محتمل يحتوي على بيانات طلاب:", {
              id: t.id,
              className: t.className,
              rows: t.rows.length,
              cols: cells.length
            });
            table = t;
            break;
          }
        }
      }
    }
  }

  if (table && table.rows.length > 1) {
    // التحقق من وجود بيانات حقيقية (ليس فقط العناوين)
    const dataRows = Array.from(table.rows).filter(row => {
      const cols = row.querySelectorAll("td");
      // مرونة في عدد الأعمدة - قبول 8 أعمدة أو أكثر
      return cols.length >= 8 && !row.classList.contains('MyPagerStyle');
    });

    console.log(`✅ تم العثور على الجدول مع ${table.rows.length} صف إجمالي، ${dataRows.length} صف بيانات`);
    console.log(`📋 معرف الجدول: ${table.id || 'غير محدد'}, الفئة: ${table.className || 'غير محدد'}`);

    if (dataRows.length > 0) {
      extractStudentsFromTable();

      // إذا تم استخراج بيانات، تابع إلى الصفحة التالية
      if (allStudents.length > 0) {
        currentPage++;
        if (currentPage <= totalPages) {
          setTimeout(() => {
            console.log(`📄 الانتقال إلى الصفحة ${currentPage}...`);
            const success = goToPage(currentPage);
            if (success) {
              setTimeout(() => waitForTableAndContinue(), 3000);
            } else {
              console.error("❌ فشل في الانتقال إلى الصفحة التالية");
              finishExtraction();
            }
          }, 1000);
        } else {
          finishExtraction();
        }
      } else {
        console.warn("⚠️ لم يتم استخراج أي بيانات من الجدول");
        if (retry < 5) {
          setTimeout(() => waitForTableAndContinue(retry + 1), 3000);
        } else {
          finishExtraction();
        }
      }
    } else {
      console.warn("⚠️ الجدول موجود لكن لا يحتوي على بيانات صالحة");
      if (retry < 10) {
        setTimeout(() => waitForTableAndContinue(retry + 1), 2000);
      } else {
        finishExtraction();
      }
    }
  } else {
    if (retry < 20) { // زيادة عدد المحاولات
      console.log(`⏳ لم يتم العثور على الجدول، المحاولة ${retry + 1}/20...`);

      // إعادة تشخيص الصفحة كل 5 محاولات
      if (retry % 5 === 0) {
        console.log("🔄 إعادة تشخيص الصفحة...");
        diagnosePage();
      }

      setTimeout(() => waitForTableAndContinue(retry + 1), 3000); // زيادة وقت الانتظار
    } else {
      console.warn("⚠️ لم يظهر الجدول في الوقت المحدد.");
      console.log("🔍 تشخيص نهائي للصفحة:");
      diagnosePage();

      // محاولة أخيرة للعثور على أي جدول يحتوي على بيانات
      const allTables = document.querySelectorAll("table");
      console.log("📋 جميع الجداول في الصفحة:");
      allTables.forEach((t, i) => {
        console.log(`جدول ${i + 1}:`, {
          id: t.id || 'بدون معرف',
          className: t.className || 'بدون فئة',
          rows: t.rows.length,
          cols: t.rows[0]?.cells.length || 0,
          hasData: t.rows.length > 1 && t.rows[1]?.cells.length > 5
        });
      });

      // إعادة تعيين حالة الاستخراج
      isExtracting = false;

      // إشعار المستخدم
      if (typeof alert !== 'undefined') {
        alert("❌ لم يتم العثور على جدول البيانات.\n\nتأكد من:\n1. إجراء البحث أولاً\n2. وجود نتائج للبحث\n3. تحميل الصفحة بالكامل");
      }
    }
  }
}

// وظيفة منفصلة لإنهاء الاستخراج وحفظ البيانات
async function finishExtraction() {
  console.log(`✅ اكتمل استخراج ${allStudents.length} طالبًا من ${totalPages} صفحة`);

  if (allStudents.length === 0) {
    console.warn("⚠️ لم يتم استخراج أي بيانات");
    isExtracting = false;
    if (typeof alert !== 'undefined') {
      alert("⚠️ لم يتم استخراج أي بيانات.\n\nتأكد من وجود نتائج للبحث.");
    }
    return;
  }

  // تعطيل جلب التفاصيل مؤقتاً لاختبار التنقل بين الصفحات
  console.log(`⏸️ تم تعطيل جلب التفاصيل مؤقتاً (${detailsQueue.length} طالب في قائمة الانتظار)`);

  // بدء جلب التفاصيل الكاملة للطلاب (معطل مؤقتاً)
  // if (detailsQueue.length > 0) {
  //   console.log(`🔄 بدء جلب التفاصيل الكاملة لـ ${detailsQueue.length} طالب...`);
  //   await processDetailsQueue();
  // }

  // حفظ البيانات
  saveStudentsData();
}

// وظيفة حفظ البيانات منفصلة
function saveStudentsData() {

  // إضافة معلومات إضافية للبيانات المحفوظة
  const dataToSave = {
    studentsData: allStudents,
    extractionInfo: {
      totalStudents: allStudents.length,
      totalPages: totalPages,
      extractionDate: new Date().toISOString(),
      extractionTime: new Date().toLocaleString('ar-SA'),
      source: 'نظام نور - إدارة المستخدمين'
    }
  };

  console.log("💾 البيانات التي سيتم حفظها:", dataToSave);

  // حفظ البيانات بطريقة مباشرة
  chrome.storage.local.set({
    studentsData: allStudents,
    allStudents: allStudents,
    totalStudentsCount: allStudents.length
  }, () => {
    if (chrome.runtime.lastError) {
      console.error("❌ خطأ في حفظ البيانات:", chrome.runtime.lastError);
      return;
    }

    console.log(`💾 تم حفظ ${allStudents.length} طالب في التخزين المحلي`);

    // التحقق من الحفظ
    chrome.storage.local.get(['studentsData'], (result) => {
      if (result.studentsData && result.studentsData.length > 0) {
        console.log(`✅ تم التحقق من الحفظ: ${result.studentsData.length} طالب`);
      } else {
        console.error("❌ فشل في الحفظ");
      }
    });

    // إشعار المستخدم بالانتهاء
    if (typeof alert !== 'undefined') {
      alert(`✅ تم استخراج ${allStudents.length} طالبًا بنجاح!\n\nتم جلب التفاصيل الكاملة لجميع الطلاب.\n\nيمكنك الآن فتح نافذة الإضافة لعرض النتائج.`);
    }

    // إرسال رسالة للـ popup لتحديث البيانات
    try {
      chrome.runtime.sendMessage({
        action: 'dataExtracted',
        studentsCount: allStudents.length,
        totalPages: totalPages
      });
    } catch (error) {
      console.log("📢 لا يمكن إرسال رسالة للـ popup (هذا طبيعي إذا لم يكن مفتوحاً)");
    }

    // إعادة تعيين حالة الاستخراج
    isExtracting = false;
    chrome.storage.local.set({ isExtracting: false });
  });
}

// وظيفة تشخيص الصفحة
function diagnosePage() {
  console.log("🔍 تشخيص الصفحة:");
  console.log("📍 URL:", window.location.href);
  console.log("📄 العنوان:", document.title);

  // فحص الجداول
  const tables = document.querySelectorAll("table");
  console.log(`📋 عدد الجداول: ${tables.length}`);

  tables.forEach((table, i) => {
    const hasData = table.rows.length > 1 && table.rows[1]?.cells.length > 5;
    console.log(`جدول ${i + 1}:`, {
      id: table.id,
      className: table.className,
      rows: table.rows.length,
      cols: table.rows[0]?.cells.length || 0,
      hasData: hasData
    });

    // إذا كان الجدول يحتوي على بيانات، اعرض عينة
    if (hasData) {
      const firstDataRow = table.rows[1];
      const cells = Array.from(firstDataRow.cells).map(cell => cell.textContent.trim().substring(0, 20));
      console.log(`📄 عينة من البيانات:`, cells);
    }
  });

  // فحص الجدول المستهدف
  const targetTable = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
  if (targetTable) {
    console.log("✅ تم العثور على الجدول المستهدف");
  } else {
    console.log("❌ لم يتم العثور على الجدول المستهدف");

    // البحث عن جداول محتملة
    const potentialTables = Array.from(tables).filter(table =>
      table.rows.length > 1 &&
      table.rows[1]?.cells.length >= 8 &&
      !table.id.includes('Instruction')
    );

    if (potentialTables.length > 0) {
      console.log("🔍 جداول محتملة تحتوي على بيانات:");
      potentialTables.forEach((table, i) => {
        console.log(`جدول محتمل ${i + 1}:`, {
          id: table.id,
          className: table.className,
          rows: table.rows.length,
          cols: table.rows[1]?.cells.length
        });
      });
    }
  }

  // فحص وجود رسائل خطأ أو "لا توجد نتائج"
  const noResultsMessages = [
    'لا توجد نتائج',
    'لم يتم العثور على نتائج',
    'No results found',
    'لا توجد بيانات'
  ];

  const pageText = document.body.textContent;
  const hasNoResults = noResultsMessages.some(msg => pageText.includes(msg));

  if (hasNoResults) {
    console.log("⚠️ يبدو أن البحث لم يُرجع نتائج");
  }
}

// مراقبة الضغط على زر البحث وتغييرات الصفحة
function setupSearchButtonObserver() {
  console.log("🔍 إعداد مراقبة زر البحث والتغييرات...");

  // مراقبة الضغط على أزرار البحث
  const searchButtons = document.querySelectorAll("input[type='submit'], button[type='submit'], input[value*='بحث']");

  searchButtons.forEach(button => {
    button.addEventListener('click', () => {
      console.log("🔍 تم اكتشاف الضغط على زر البحث");

      // انتظار قليل ثم بدء المراقبة
      setTimeout(() => {
        console.log("🔄 بدء مراقبة ظهور النتائج...");
        setupResultsObserver();
      }, 2000);
    });
  });

  // مراقبة تلقائية لتغييرات الصفحة (في حالة عدم كشف الضغط)
  console.log("🔄 بدء المراقبة التلقائية لتغييرات الصفحة...");
  setupAutomaticResultsObserver();
}

// مراقبة تلقائية لتغييرات الصفحة
function setupAutomaticResultsObserver() {
  let attempts = 0;
  const maxAttempts = 30; // زيادة عدد المحاولات
  let lastPageState = checkPageState();

  const checkForChanges = () => {
    attempts++;
    const currentPageState = checkPageState();

    console.log(`🔄 فحص تلقائي ${attempts}/${maxAttempts} - الحالة: ${currentPageState}`);

    // إجراء اختبار يدوي كل 5 محاولات
    if (attempts % 5 === 0) {
      console.log("🔍 إجراء اختبار يدوي مفصل...");
      manualDataDetection();
    }

    // إذا تغيرت حالة الصفحة من search_ready إلى data_ready
    if (lastPageState === 'search_ready' && currentPageState === 'data_ready') {
      console.log("✅ تم اكتشاف تغيير الحالة إلى data_ready، بدء الاستخراج...");
      setTimeout(startExtraction, 1000);
      return;
    }

    // إذا كانت البيانات جاهزة
    if (currentPageState === 'data_ready') {
      console.log("✅ تم اكتشاف وجود بيانات، بدء الاستخراج...");
      setTimeout(startExtraction, 1000);
      return;
    }

    // إذا ظهرت رسالة خطأ
    if (currentPageState === 'error' && lastPageState !== 'error') {
      console.log("❌ تم اكتشاف رسالة خطأ جديدة");
    }

    lastPageState = currentPageState;

    if (attempts < maxAttempts) {
      setTimeout(checkForChanges, 3000); // فحص كل 3 ثوان
    } else {
      console.log("⏰ انتهت مهلة المراقبة التلقائية");
      console.log("🔍 إجراء اختبار يدوي نهائي...");
      manualDataDetection();
    }
  };

  // بدء الفحص بعد 5 ثوان
  setTimeout(checkForChanges, 5000);
}

// مراقبة ظهور النتائج بعد البحث
function setupResultsObserver() {
  let attempts = 0;
  const maxAttempts = 15;

  const checkForResults = () => {
    attempts++;
    console.log(`🔍 فحص النتائج - المحاولة ${attempts}/${maxAttempts}`);

    const pageState = checkPageState();

    if (pageState === 'data_ready') {
      console.log("✅ تم اكتشاف ظهور النتائج، بدء الاستخراج...");
      setTimeout(startExtraction, 1000);
      return;
    }

    if (pageState === 'error') {
      console.log("❌ البحث لم يُرجع نتائج");
      return;
    }

    if (attempts < maxAttempts) {
      setTimeout(checkForResults, 2000);
    } else {
      console.log("⏰ انتهت مهلة انتظار النتائج");
    }
  };

  checkForResults();
}

// وظيفة مراقبة تغييرات DOM
function setupDOMObserver() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // تحقق من إضافة جداول جديدة
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const tables = node.querySelectorAll ? node.querySelectorAll('table') : [];
            if (tables.length > 0 || node.tagName === 'TABLE') {
              console.log("🔄 تم اكتشاف إضافة جدول جديد");

              // فحص إذا كان الجدول يحتوي على بيانات
              const hasData = Array.from(tables).some(table =>
                table.rows.length > 1 && table.rows[1]?.cells.length >= 8
              );

              if (hasData && !isExtracting) {
                console.log("✅ الجدول الجديد يحتوي على بيانات، بدء الاستخراج...");
                setTimeout(startExtraction, 1000);
              }
            }
          }
        });
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // إيقاف المراقب بعد 60 ثانية
  setTimeout(() => {
    observer.disconnect();
    console.log("🔄 تم إيقاف مراقب DOM");
  }, 60000);
}

// فحص حالة الصفحة قبل البدء
function checkPageState() {
  console.log("🔍 فحص حالة الصفحة...");

  // أولاً: فحص وجود جداول بيانات (أولوية عالية)
  const tables = document.querySelectorAll("table");
  console.log(`📋 عدد الجداول الموجودة: ${tables.length}`);

  let hasDataTables = false;

  // فحص أكثر تفصيلاً للجداول
  for (const table of tables) {
    if (table.rows.length > 1) {
      const firstDataRow = table.rows[1];
      const cells = firstDataRow?.cells;

      if (cells && cells.length >= 5) { // تقليل الحد الأدنى إلى 5 أعمدة
        // فحص محتوى الخلايا للتأكد من وجود بيانات طلاب
        const cellTexts = Array.from(cells).map(cell => cell.textContent.trim());
        console.log(`🔍 فحص جدول: ${table.id || table.className} - عينة البيانات:`, cellTexts.slice(0, 3));

        // البحث عن أنماط بيانات الطلاب
        const hasStudentData = cellTexts.some(text =>
          /^\d{8,}$/.test(text) || // رقم هوية (8 أرقام أو أكثر)
          /الصف|المتوسط|الثانوي|الابتدائي|الأول|الثاني|الثالث/.test(text) || // أسماء صفوف
          (/^[a-zA-Z0-9]+$/.test(text) && text.length > 3) // اسم مستخدم محتمل
        );

        // فحص وجود أسماء عربية (أسماء طلاب)
        const hasArabicNames = cellTexts.some(text =>
          /[\u0600-\u06FF]/.test(text) &&
          text.length > 5 &&
          !text.includes('الصف') &&
          !text.includes('البحث') &&
          !text.includes('المدرسة')
        );

        // فحص وجود أسماء إنجليزية (أسماء طلاب بالإنجليزية)
        const hasEnglishNames = cellTexts.some(text =>
          /^[A-Z][a-z]+ [A-Z][a-z]+/.test(text) && text.length > 8
        );

        if (hasStudentData || hasArabicNames || hasEnglishNames) {
          console.log("✅ تم العثور على جدول يحتوي على بيانات طلاب:", {
            id: table.id || 'بدون معرف',
            className: table.className || 'بدون فئة',
            rows: table.rows.length,
            cols: cells.length,
            hasStudentData,
            hasArabicNames,
            hasEnglishNames,
            sampleData: cellTexts.slice(0, 5)
          });
          hasDataTables = true;
          break;
        }
      }
    }
  }

  if (hasDataTables) {
    console.log("✅ حالة الصفحة: data_ready");
    return 'data_ready';
  }

  // ثانياً: فحص وجود رسائل خطأ أو تعليمات
  const pageText = document.body.textContent;

  const errorMessages = [
    'لم يتم العثور على جدول البيانات',
    'لا توجد نتائج',
    'لم يتم العثور على نتائج',
    'No results found',
    'إجراء البحث أولاً',
    'وجود نتائج للبحث'
  ];

  const hasError = errorMessages.some(msg => pageText.includes(msg));

  if (hasError) {
    console.log("⚠️ حالة الصفحة: error - تم اكتشاف رسالة خطأ");
    return 'error';
  }

  // ثالثاً: فحص وجود أزرار البحث
  const searchButton = document.querySelector("input[type='submit'][value*='بحث']") ||
                      document.querySelector("button[type='submit']") ||
                      document.querySelector("input[value='بحث']");

  if (searchButton) {
    console.log("🔍 حالة الصفحة: search_ready - تم العثور على زر البحث");
    return 'search_ready';
  }



  console.log("❓ حالة الصفحة: unknown - غير واضحة");
  return 'unknown';
}

// وظيفة اختبار يدوي للكشف عن البيانات
function manualDataDetection() {
  console.log("🔍 اختبار يدوي للكشف عن البيانات...");

  const tables = document.querySelectorAll("table");
  console.log(`📋 إجمالي الجداول: ${tables.length}`);

  tables.forEach((table, index) => {
    console.log(`جدول ${index + 1}:`, {
      id: table.id || 'بدون معرف',
      className: table.className || 'بدون فئة',
      rows: table.rows.length,
      cols: table.rows[0]?.cells.length || 0
    });

    if (table.rows.length > 1) {
      const firstDataRow = table.rows[1];
      const cells = firstDataRow?.cells;
      if (cells && cells.length >= 5) {
        const cellTexts = Array.from(cells).map(cell => cell.textContent.trim());
        console.log(`  عينة البيانات:`, cellTexts.slice(0, 5));

        // اختبار الأنماط
        const patterns = {
          hasNumbers: cellTexts.some(text => /^\d{8,}$/.test(text)),
          hasGrades: cellTexts.some(text => /الصف|المتوسط|الثانوي|الابتدائي|الأول|الثاني|الثالث/.test(text)),
          hasUsernames: cellTexts.some(text => /^[a-zA-Z0-9]+$/.test(text) && text.length > 3),
          hasArabicNames: cellTexts.some(text => /[\u0600-\u06FF]/.test(text) && text.length > 5),
          hasEnglishNames: cellTexts.some(text => /^[A-Z][a-z]+ [A-Z][a-z]+/.test(text))
        };

        console.log(`  الأنماط المكتشفة:`, patterns);

        if (patterns.hasNumbers || patterns.hasGrades || patterns.hasArabicNames || patterns.hasEnglishNames) {
          console.log(`  ✅ هذا الجدول يحتوي على بيانات طلاب محتملة!`);
        }
      }
    }
  });
}

// وظيفة بدء الاستخراج
function startExtraction() {
  if (isExtracting) {
    console.log("⚠️ عملية الاستخراج جارية بالفعل...");
    return;
  }

  // فحص حالة الصفحة أولاً
  const pageState = checkPageState();

  if (pageState === 'error') {
    console.log("❌ الصفحة تحتوي على رسالة خطأ - لا يمكن المتابعة");
    if (typeof alert !== 'undefined') {
      alert(`❌ لا يمكن استخراج البيانات\n\nالأسباب المحتملة:\n• لم يتم إجراء البحث بعد\n• لا توجد نتائج للبحث\n• تحقق من معايير البحث\n\nالحل:\n1. اضغط زر "بحث" في نور\n2. تأكد من ظهور نتائج\n3. أعد تشغيل الإضافة`);
    }
    return;
  }

  if (pageState === 'search_ready') {
    console.log("🔍 الصفحة جاهزة للبحث - يجب إجراء البحث أولاً");
    if (typeof alert !== 'undefined') {
      alert(`🔍 يجب إجراء البحث أولاً\n\nالخطوات:\n1. اختر معايير البحث (المدرسة، الصف، إلخ)\n2. اضغط زر "بحث"\n3. انتظر ظهور النتائج\n4. أعد تشغيل الإضافة`);
    }
    return;
  }

  isExtracting = true;

  // حفظ حالة الاستخراج
  chrome.storage.local.set({ isExtracting: true });

  console.log("🔍 محاولة العثور على جدول الطلاب...");

  // تشخيص الصفحة أولاً
  diagnosePage();

  totalPages = getTotalPages();
  console.log(`📄 عدد الصفحات الكلي: ${totalPages}`);
  waitForTableAndContinue();
}

// وظيفة البحث التلقائي
function performAutomaticSearch() {
  console.log("🤖 بدء البحث التلقائي...");

  // البحث عن زر البحث بطرق متعددة
  const searchButton = document.querySelector("input[type='submit'][value*='بحث']") ||
                      document.querySelector("button[type='submit']") ||
                      document.querySelector("input[value='بحث']") ||
                      document.querySelector("input[id*='Search']") ||
                      document.querySelector("button[id*='Search']") ||
                      document.querySelector("input[id*='btnSearch']") ||
                      document.querySelector("button[id*='btnSearch']");

  if (searchButton) {
    console.log("🔍 تم العثور على زر البحث، سيتم الضغط عليه تلقائياً...");
    console.log("🔍 معلومات الزر:", {
      id: searchButton.id,
      value: searchButton.value,
      type: searchButton.type,
      tagName: searchButton.tagName
    });

    // الضغط على زر البحث تلقائياً
    searchButton.click();

    // بدء مراقبة ظهور النتائج
    setTimeout(() => {
      console.log("🔄 بدء مراقبة ظهور النتائج بعد البحث التلقائي...");
      setupResultsObserver();
    }, 3000);

    return true;
  } else {
    console.log("❌ لم يتم العثور على زر البحث");
    return false;
  }
}

// بدء التنفيذ
setTimeout(() => {
  console.log("🚀 بدء تشغيل إضافة نور...");

  // فحص حالة الصفحة أولاً
  const pageState = checkPageState();
  console.log(`📊 حالة الصفحة: ${pageState}`);

  if (pageState === 'data_ready') {
    // إذا كانت البيانات جاهزة، ابدأ الاستخراج مباشرة
    console.log("✅ البيانات جاهزة، بدء الاستخراج...");
    startExtraction();
  } else if (pageState === 'search_ready') {
    // إذا كانت الصفحة جاهزة للبحث، ابدأ المراقبة التلقائية
    console.log("🤖 الصفحة جاهزة للبحث، سيتم البحث تلقائياً...");

    // انتظار قصير ثم البحث التلقائي
    setTimeout(() => {
      const searchSuccess = performAutomaticSearch();

      if (!searchSuccess) {
        // إذا فشل البحث التلقائي، ابدأ المراقبة
        console.log("🔄 فشل البحث التلقائي، بدء المراقبة...");
        setupSearchButtonObserver();
      }
    }, 2000);

    // إشعار المستخدم
    console.log("� إضافة نور تعمل في الخلفية وستبدأ تلقائياً عند ظهور النتائج");
  } else if (pageState === 'error') {
    // إذا كانت هناك رسالة خطأ، جرب البحث التلقائي أيضاً
    console.log("❌ رسالة خطأ موجودة، لكن سأجرب البحث التلقائي...");
    setTimeout(() => {
      const searchSuccess = performAutomaticSearch();
      if (!searchSuccess) {
        setupSearchButtonObserver();
      }
    }, 2000);
  } else {
    // حالة غير واضحة، جرب البحث التلقائي
    console.log("❓ حالة غير واضحة، محاولة البحث التلقائي...");
    setTimeout(() => {
      const searchSuccess = performAutomaticSearch();
      if (!searchSuccess) {
        setupSearchButtonObserver();
      }
    }, 2000);
  }

  // بدء مراقبة DOM للتغييرات في جميع الحالات
  setupDOMObserver();
}, 3000); // زيادة وقت الانتظار لضمان تحميل الصفحة
