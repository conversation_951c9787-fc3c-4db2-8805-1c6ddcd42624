console.log("📦 إضافة نور: بدء مراقبة الصفحة بعد الضغط على بحث...");

let allStudents = [];
let currentPage = 1;
let totalPages = 1;
let isExtracting = false;

function extractStudentsFromTable() {
  // البحث عن الجدول بطرق متعددة
  let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (!table) {
    table = document.querySelector("table[id*='gvUserInfo']");
  }

  if (!table) {
    table = document.querySelector("table.GridClass");
  }

  if (!table) {
    // البحث عن أي جدول يحتوي على بيانات الطلاب
    const tables = document.querySelectorAll("table");
    for (const t of tables) {
      const headers = t.querySelectorAll("th");
      if (headers.length > 5) {
        table = t;
        break;
      }
    }
  }

  if (!table) {
    console.warn("❌ لم يتم العثور على جدول البيانات");
    return;
  }

  console.log("✅ تم العثور على الجدول:", table.id || table.className);

  const rows = table.querySelectorAll("tr") || [];
  let count = 0;

  rows.forEach((row) => {
    const cols = row.querySelectorAll("td");

    // تجاهل صف التنقل بين الصفحات (MyPagerStyle)
    if (row.classList.contains('MyPagerStyle')) {
      return;
    }

    // التأكد من وجود 11 عمود كما في الجدول الحقيقي
    if (cols.length >= 11) {
      // استخراج اسم المستخدم من الرابط في العمود الأول
      const usernameLink = cols[0].querySelector('a');
      const username = usernameLink ? usernameLink.textContent.trim() : cols[0].textContent.trim();

      const studentData = {
        username: username,                           // العمود 0: اسم المستخدم
        name: cols[1]?.textContent?.trim() || '',     // العمود 1: الاسم الرباعي
        name_english: cols[2]?.textContent?.trim() || '', // العمود 2: الاسم بالإنجليزية
        national_id: cols[3]?.textContent?.trim() || '',  // العمود 3: رقم السجل المدني
        study_system: cols[4]?.textContent?.trim() || '', // العمود 4: النظام الدراسي
        grade: cols[5]?.textContent?.trim() || '',         // العمود 5: الصف
        department: cols[6]?.textContent?.trim() || '',    // العمود 6: القسم
        class: cols[7]?.textContent?.trim() || '',         // العمود 7: الفصل
        enrollment_status: cols[8]?.textContent?.trim() || '', // العمود 8: حالة القيد
        verification: cols[9]?.textContent?.trim() || '',      // العمود 9: التدقيق
        record_status: cols[10]?.textContent?.trim() || ''     // العمود 10: حالة السجل
      };

      // تجاهل الصفوف الفارغة أو صفوف العناوين
      if (studentData.name &&
          studentData.name !== 'الاسم الرباعي' &&
          studentData.name.length > 2 &&
          studentData.national_id &&
          studentData.national_id !== 'رقم السجل المدني') {
        allStudents.push(studentData);
        count++;
        console.log(`📝 طالب ${count}:`, studentData.name, `- ID: ${studentData.national_id}`);
      }
    }
  });

  console.log(`✅ تم استخراج ${count} طالبًا من الصفحة ${currentPage}`);
}

function getTotalPages() {
  // البحث عن صف التنقل
  const pagerRow = document.querySelector("tr.MyPagerStyle");
  if (!pagerRow) {
    console.log("❌ لم يتم العثور على صف التنقل");
    return 1;
  }

  // البحث عن روابط الصفحات
  const pageLinks = pagerRow.querySelectorAll("a[href*='Page$']");
  console.log(`🔍 تم العثور على ${pageLinks.length} رابط صفحة`);

  if (pageLinks.length === 0) {
    return 1;
  }

  // العثور على أعلى رقم صفحة
  let maxPage = 1;
  pageLinks.forEach(link => {
    const match = link.href.match(/Page\$(\d+)/);
    if (match) {
      const pageNum = parseInt(match[1]);
      if (pageNum > maxPage) {
        maxPage = pageNum;
      }
    }
  });

  console.log(`📄 أعلى رقم صفحة تم العثور عليه: ${maxPage}`);
  return maxPage;
}

function triggerPostBack(target, arg) {
  // استخدام الطريقة الآمنة بدلاً من inline script
  if (typeof __doPostBack === 'function') {
    __doPostBack(target, arg);
  } else {
    console.warn('⚠️ __doPostBack غير متوفر');
  }
}

function goToPage(pageNumber) {
  // البحث عن صف التنقل
  const pagerRow = document.querySelector("tr.MyPagerStyle");
  if (!pagerRow) {
    console.warn("❌ لم يتم العثور على صف التنقل");
    return false;
  }

  // البحث عن رابط الصفحة المحددة
  const pageLinks = pagerRow.querySelectorAll("a[href*='Page$']");
  const link = Array.from(pageLinks).find(a => a.href.includes(`Page$${pageNumber}`));

  if (link) {
    console.log(`🔗 الانتقال إلى الصفحة ${pageNumber} باستخدام click()`);
    link.click();
    return true;
  } else {
    // محاولة استخدام __doPostBack إذا كان متوفراً
    const target = `ctl00$PlaceHolderMain$gvUserInfo`;
    const arg = `Page$${pageNumber}`;
    console.log(`� محاولة الانتقال باستخدام __doPostBack إلى الصفحة ${pageNumber}`);

    if (typeof __doPostBack === 'function') {
      __doPostBack(target, arg);
      return true;
    } else {
      console.warn("❌ __doPostBack غير متوفر");
      return false;
    }
  }
}
function waitForTableAndContinue(retry = 0) {
  // البحث عن الجدول بطرق متعددة
  let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (!table) {
    table = document.querySelector("table[id*='gvUserInfo']");
  }

  if (!table) {
    table = document.querySelector("table.GridClass");
  }

  console.log(`🔍 محاولة ${retry + 1}: البحث عن الجدول...`);

  if (table && table.rows.length > 1) {
    // التحقق من وجود بيانات حقيقية (ليس فقط العناوين)
    const dataRows = Array.from(table.rows).filter(row => {
      const cols = row.querySelectorAll("td");
      return cols.length >= 11 && !row.classList.contains('MyPagerStyle');
    });

    console.log(`✅ تم العثور على الجدول مع ${table.rows.length} صف إجمالي، ${dataRows.length} صف بيانات`);

    if (dataRows.length > 0) {
      extractStudentsFromTable();
      currentPage++;
      if (currentPage <= totalPages) {
        setTimeout(() => {
          console.log(`📄 الانتقال إلى الصفحة ${currentPage}...`);
          const success = goToPage(currentPage);
          if (success) {
            setTimeout(() => waitForTableAndContinue(), 3000);
          } else {
            console.error("❌ فشل في الانتقال إلى الصفحة التالية");
            isExtracting = false;
          }
        }, 1000);
      } else {
        console.log(`✅ اكتمل استخراج ${allStudents.length} طالبًا من ${totalPages} صفحة`);

        // إضافة معلومات إضافية للبيانات المحفوظة
        const dataToSave = {
          studentsData: allStudents,
          extractionInfo: {
            totalStudents: allStudents.length,
            totalPages: totalPages,
            extractionDate: new Date().toISOString(),
            extractionTime: new Date().toLocaleString('ar-SA'),
            source: 'نظام نور - إدارة المستخدمين'
          }
        };

        console.log("💾 البيانات التي سيتم حفظها:", dataToSave);

        chrome.storage.local.set(dataToSave, () => {
          if (chrome.runtime.lastError) {
            console.error("❌ خطأ في حفظ البيانات:", chrome.runtime.lastError);
            return;
          }

          console.log("💾 تم حفظ جميع بيانات الطلاب في التخزين المحلي");
          console.log(`📊 الإحصائيات: ${allStudents.length} طالب من ${totalPages} صفحة`);

          // التحقق من الحفظ
          chrome.storage.local.get(['studentsData'], (result) => {
            if (result.studentsData && result.studentsData.length > 0) {
              console.log("✅ تم التحقق من حفظ البيانات بنجاح");
            } else {
              console.error("❌ فشل في التحقق من حفظ البيانات");
            }
          });

          // إشعار المستخدم بالانتهاء
          if (typeof alert !== 'undefined') {
            alert(`✅ تم استخراج ${allStudents.length} طالبًا بنجاح!\n\nيمكنك الآن فتح نافذة الإضافة لعرض النتائج.`);
          }

          // إعادة تعيين حالة الاستخراج
          isExtracting = false;
        });
      }
    } else {
      console.warn("⚠️ الجدول موجود لكن لا يحتوي على بيانات");
      if (retry < 10) {
        setTimeout(() => waitForTableAndContinue(retry + 1), 2000);
      } else {
        isExtracting = false;
      }
    }
  } else {
    if (retry < 15) {
      console.log(`⏳ لم يتم العثور على الجدول، المحاولة ${retry + 1}/15...`);
      setTimeout(() => waitForTableAndContinue(retry + 1), 2000);
    } else {
      console.warn("⚠️ لم يظهر الجدول في الوقت المحدد.");
      console.log("🔍 الجداول الموجودة في الصفحة:");
      document.querySelectorAll("table").forEach((t, i) => {
        console.log(`جدول ${i + 1}:`, t.id || t.className, `عدد الصفوف: ${t.rows.length}`);
      });

      // إعادة تعيين حالة الاستخراج
      isExtracting = false;
    }
  }
}

// وظيفة تشخيص الصفحة
function diagnosePage() {
  console.log("🔍 تشخيص الصفحة:");
  console.log("📍 URL:", window.location.href);
  console.log("📄 العنوان:", document.title);

  // فحص الجداول
  const tables = document.querySelectorAll("table");
  console.log(`📋 عدد الجداول: ${tables.length}`);

  tables.forEach((table, i) => {
    console.log(`جدول ${i + 1}:`, {
      id: table.id,
      className: table.className,
      rows: table.rows.length,
      cols: table.rows[0]?.cells.length || 0
    });
  });

  // فحص الجدول المستهدف
  const targetTable = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
  if (targetTable) {
    console.log("✅ تم العثور على الجدول المستهدف");
  } else {
    console.log("❌ لم يتم العثور على الجدول المستهدف");
  }
}

// بدء التنفيذ
setTimeout(() => {
  if (isExtracting) {
    console.log("⚠️ عملية الاستخراج جارية بالفعل...");
    return;
  }

  isExtracting = true;
  console.log("🔍 محاولة العثور على جدول الطلاب...");

  // تشخيص الصفحة أولاً
  diagnosePage();

  totalPages = getTotalPages();
  console.log(`📄 عدد الصفحات الكلي: ${totalPages}`);
  waitForTableAndContinue();
}, 2000);
