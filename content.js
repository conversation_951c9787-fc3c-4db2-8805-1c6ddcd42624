console.log("📦 إضافة نور: بدء مراقبة الصفحة بعد الضغط على بحث...");

let allStudents = [];
let currentPage = 1;
let totalPages = 1;
let isExtracting = false;

function extractStudentsFromTable() {
  const table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
  const rows = table?.querySelectorAll("tr") || [];
  let count = 0;

  rows.forEach(row => {
    const cols = row.querySelectorAll("td");
    if (cols.length >= 8) {
      allStudents.push({
        username: cols[0].innerText.trim(),
        name: cols[1].innerText.trim(),
        national_id: cols[3].innerText.trim(),
        grade: cols[5].innerText.trim(),
        class: cols[7].innerText.trim()
      });
      count++;
    }
  });

  console.log(`✅ تم استخراج ${count} طالبًا من الصفحة ${currentPage}`);
}

function getTotalPages() {
  const pager = document.querySelector(".MyPagerStyle");
  if (!pager) return 1;
  const pageLinks = pager.querySelectorAll("a[href*='Page$']");
  const lastLink = Array.from(pageLinks).pop();
  const match = lastLink?.href?.match(/Page\$(\d+)/);
  return match ? parseInt(match[1]) : 1;
}

function triggerPostBack(target, arg) {
  // استخدام الطريقة الآمنة بدلاً من inline script
  if (typeof __doPostBack === 'function') {
    __doPostBack(target, arg);
  } else {
    console.warn('⚠️ __doPostBack غير متوفر');
  }
}

function goToPage(pageNumber) {
  const pager = document.querySelector(".MyPagerStyle");
  const pageLinks = pager?.querySelectorAll("a[href*='Page$']") || [];
  const link = Array.from(pageLinks).find(a => a.href.includes(`Page$${pageNumber}`));

  if (link) {
    // استخدام click() بدلاً من JavaScript URL
    console.log(`🔗 الانتقال إلى الصفحة ${pageNumber} باستخدام click()`);
    link.click();
    return true;
  } else {
    // محاولة استخدام __doPostBack إذا كان متوفراً
    const target = `ctl00$PlaceHolderMain$gvUserInfo`;
    const arg = `Page$${pageNumber}`;
    console.log(`� محاولة الانتقال باستخدام __doPostBack إلى الصفحة ${pageNumber}`);
    triggerPostBack(target, arg);
    return true;
  }
}
function waitForTableAndContinue(retry = 0) {
  const table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");

  if (table && table.rows.length > 1) {
    extractStudentsFromTable();
    currentPage++;
    if (currentPage <= totalPages) {
      setTimeout(() => {
        console.log(`📄 الانتقال إلى الصفحة ${currentPage}...`);
        goToPage(currentPage);
        setTimeout(() => waitForTableAndContinue(), 3000);
      }, 1000);
    } else {
      console.log(`✅ اكتمل استخراج ${allStudents.length} طالبًا من ${totalPages} صفحة`);

      // إضافة معلومات إضافية للبيانات المحفوظة
      const dataToSave = {
        studentsData: allStudents,
        extractionInfo: {
          totalStudents: allStudents.length,
          totalPages: totalPages,
          extractionDate: new Date().toISOString(),
          extractionTime: new Date().toLocaleString('ar-SA'),
          source: 'نظام نور - إدارة المستخدمين'
        }
      };

      console.log("💾 البيانات التي سيتم حفظها:", dataToSave);

      chrome.storage.local.set(dataToSave, () => {
        console.log("💾 تم حفظ جميع بيانات الطلاب في التخزين المحلي");
        console.log(`📊 الإحصائيات: ${allStudents.length} طالب من ${totalPages} صفحة`);

        // إشعار المستخدم بالانتهاء
        if (typeof alert !== 'undefined') {
          alert(`✅ تم استخراج ${allStudents.length} طالبًا بنجاح!\n\nيمكنك الآن فتح نافذة الإضافة لعرض النتائج.`);
        }

        // إعادة تعيين حالة الاستخراج
        isExtracting = false;
      });
    }
  } else {
    if (retry < 10) {
      setTimeout(() => waitForTableAndContinue(retry + 1), 1000);
    } else {
      console.warn("⚠️ لم يظهر الجدول في الوقت المحدد.");
    }
  }
}

// بدء التنفيذ
setTimeout(() => {
  if (isExtracting) {
    console.log("⚠️ عملية الاستخراج جارية بالفعل...");
    return;
  }

  isExtracting = true;
  console.log("🔍 محاولة العثور على جدول الطلاب...");
  totalPages = getTotalPages();
  console.log(`📄 عدد الصفحات الكلي: ${totalPages}`);
  waitForTableAndContinue();
}, 2000);
