// الحل النهائي البسيط - استخراج مباشر
console.log("🚀 بدء الحل النهائي البسيط");

let allStudents = [];

// استخراج البيانات مباشرة من أي جدول
function extractData() {
  console.log("🔍 البحث عن البيانات في الصفحة...");
  
  // البحث في جميع الجداول
  const tables = document.querySelectorAll('table');
  console.log(`📊 عدد الجداول: ${tables.length}`);
  
  let studentsFound = 0;
  
  tables.forEach((table, tableIndex) => {
    console.log(`🔍 فحص الجدول ${tableIndex + 1}...`);
    
    const rows = table.querySelectorAll('tr');
    console.log(`📋 عدد الصفوف: ${rows.length}`);
    
    rows.forEach((row, rowIndex) => {
      const cells = row.querySelectorAll('td');
      
      // إذا كان الصف يحتوي على 8+ خلايا، قد يكون بيانات طالب
      if (cells.length >= 8) {
        const cellTexts = Array.from(cells).map(cell => cell.textContent.trim());
        
        // تحقق من وجود بيانات طالب (اسم ورقم هوية)
        const hasName = cellTexts.some(text => text.length > 5 && /[أ-ي]/.test(text));
        const hasID = cellTexts.some(text => /^\d{10}$/.test(text) || /^[A-Z0-9]{10,}$/.test(text));
        
        if (hasName && hasID) {
          const student = {
            username: cellTexts[0] || '',
            name: cellTexts[1] || '',
            name_english: cellTexts[2] || '',
            national_id: cellTexts[3] || '',
            study_system: cellTexts[4] || '',
            grade: cellTexts[5] || '',
            department: cellTexts[6] || '',
            class: cellTexts[7] || '',
            enrollment_status: cellTexts[8] || '',
            verification: cellTexts[9] || '',
            record_status: cellTexts[10] || ''
          };
          
          // تجنب التكرار
          const exists = allStudents.some(s => s.username === student.username || s.national_id === student.national_id);
          
          if (!exists && student.name.length > 2) {
            allStudents.push(student);
            studentsFound++;
            console.log(`✅ طالب ${studentsFound}: ${student.name} - ${student.national_id}`);
          }
        }
      }
    });
  });
  
  console.log(`📊 تم العثور على ${studentsFound} طالب في هذه الصفحة`);
  return studentsFound;
}

// البحث عن روابط التنقل
function getNextPageLink() {
  // البحث عن روابط تحتوي على "Page$"
  const links = document.querySelectorAll('a[href*="Page$"]');
  console.log(`🔗 عدد روابط التنقل: ${links.length}`);
  
  // البحث عن أول رابط غير مستخدم
  for (const link of links) {
    const href = link.getAttribute('href');
    const text = link.textContent.trim();
    
    // تجاهل الصفحة الحالية (span) والبحث عن رقم أكبر
    if (href && /Page\$(\d+)/.test(href)) {
      const pageNum = parseInt(href.match(/Page\$(\d+)/)[1]);
      console.log(`🔍 رابط الصفحة ${pageNum}: ${text}`);
      
      // إذا كان رقم الصفحة أكبر من الحالي
      if (pageNum > getCurrentPage()) {
        console.log(`✅ سيتم الانتقال للصفحة ${pageNum}`);
        return link;
      }
    }
  }
  
  return null;
}

// الحصول على رقم الصفحة الحالية
function getCurrentPage() {
  // البحث عن span يحتوي على رقم الصفحة الحالية
  const spans = document.querySelectorAll('span');
  for (const span of spans) {
    const text = span.textContent.trim();
    if (/^\d+$/.test(text) && parseInt(text) >= 1) {
      const parent = span.parentElement;
      if (parent && parent.innerHTML.includes('Page$')) {
        return parseInt(text);
      }
    }
  }
  return 1;
}

// الانتقال للصفحة التالية
function goToNextPage() {
  const nextLink = getNextPageLink();
  
  if (nextLink) {
    const href = nextLink.getAttribute('href');
    console.log(`🚀 الانتقال للصفحة التالية: ${href}`);
    
    try {
      // تنفيذ JavaScript مباشرة
      if (href.startsWith('javascript:')) {
        eval(href.replace('javascript:', ''));
        return true;
      } else {
        nextLink.click();
        return true;
      }
    } catch (error) {
      console.error('❌ خطأ في الانتقال:', error);
      return false;
    }
  } else {
    console.log('✅ لا توجد صفحات أخرى');
    return false;
  }
}

// العملية الرئيسية
function processPage() {
  console.log(`📄 معالجة الصفحة ${getCurrentPage()}...`);
  
  const studentsFound = extractData();
  
  if (studentsFound > 0) {
    console.log(`✅ تم استخراج ${studentsFound} طالب (إجمالي: ${allStudents.length})`);
    
    // محاولة الانتقال للصفحة التالية
    setTimeout(() => {
      if (goToNextPage()) {
        // انتظار تحميل الصفحة الجديدة
        setTimeout(processPage, 3000);
      } else {
        // انتهاء جميع الصفحات
        finishExtraction();
      }
    }, 2000);
  } else {
    console.log('❌ لم يتم العثور على بيانات طلاب');
    finishExtraction();
  }
}

// إنهاء الاستخراج وحفظ البيانات
function finishExtraction() {
  console.log(`🎉 اكتمل الاستخراج: ${allStudents.length} طالب`);
  
  if (allStudents.length > 0) {
    // حفظ البيانات
    chrome.storage.local.set({
      studentsData: allStudents,
      totalStudentsCount: allStudents.length,
      extractionDate: new Date().toISOString()
    }, () => {
      console.log(`💾 تم حفظ ${allStudents.length} طالب`);
      alert(`✅ تم استخراج ${allStudents.length} طالبًا بنجاح!\n\nيمكنك الآن فتح popup الإضافة لعرض النتائج.`);
    });
  } else {
    alert('❌ لم يتم العثور على بيانات طلاب في هذه الصفحة');
  }
}

// بدء التشغيل فوراً
console.log('🚀 بدء الاستخراج المباشر...');
setTimeout(processPage, 1000);
