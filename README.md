# إضافة جلب طلاب نور

إضافة متصفح لاستخراج بيانات الطلاب من نظام نور التعليمي بشكل آمن وفعال.

## المميزات

✅ **استخراج آمن**: حل مشكلة Content Security Policy (CSP)  
✅ **واجهة محسنة**: عرض البيانات مع إحصائيات مفصلة  
✅ **تصدير البيانات**: إمكانية تصدير البيانات إلى ملف CSV  
✅ **إرسال للسيرفر**: إرسال البيانات إلى API خارجي  
✅ **معلومات الاستخراج**: تتبع تاريخ ووقت الاستخراج  

## كيفية الاستخدام

### 1. تثبيت الإضافة
1. افتح Chrome وانتقل إلى `chrome://extensions/`
2. فعل "وضع المطور" (Developer mode)
3. اضغط "تحميل إضافة غير مُعبأة" (Load unpacked)
4. اختر مجلد الإضافة

### 2. استخراج البيانات
1. اذهب إلى نظام نور: `https://noor.moe.gov.sa/Noor/UsersManagement/UserSearch.aspx`
2. قم بإعداد البحث (اختر المدرسة، الصف، إلخ)
3. اضغط على زر "بحث"
4. ستبدأ الإضافة تلقائياً في استخراج البيانات من جميع الصفحات
5. ستظهر رسالة تأكيد عند الانتهاء

### 3. عرض النتائج
1. اضغط على أيقونة الإضافة في شريط الأدوات
2. ستظهر نافذة تحتوي على:
   - إحصائيات البيانات (عدد الطلاب، الصفوف، الفصول)
   - جدول بجميع بيانات الطلاب
   - أزرار للتحديث والتصدير والإرسال

## الملفات الرئيسية

- `manifest.json`: إعدادات الإضافة
- `content.js`: سكريبت استخراج البيانات من نور
- `popup.html`: واجهة عرض النتائج
- `README.md`: دليل الاستخدام

## الحلول المطبقة

### مشكلة Content Security Policy (CSP)
- **المشكلة**: `Refused to run the JavaScript URL because it violates CSP`
- **الحل**: استبدال `__doPostBack` inline scripts بـ `click()` events آمنة
- **النتيجة**: تجنب انتهاك سياسات الأمان

### تحسينات الواجهة
- تصميم عصري مع gradients وألوان جذابة
- إحصائيات مفصلة للبيانات المستخرجة
- جدول قابل للتمرير مع hover effects
- أزرار تفاعلية مع loading states

### وظائف إضافية
- تصدير البيانات إلى CSV
- إرسال البيانات إلى API خارجي
- حفظ معلومات الاستخراج (التاريخ، الوقت، المصدر)
- حماية من التشغيل المتكرر

## البيانات المستخرجة

يتم استخراج البيانات التالية لكل طالب:
- اسم المستخدم
- الاسم الكامل
- رقم الهوية الوطنية
- الصف الدراسي
- الفصل

## ملاحظات مهمة

⚠️ **تأكد من**:
- وجود اتصال مستقر بالإنترنت
- عدم إغلاق نافذة نور أثناء الاستخراج
- انتظار انتهاء العملية قبل إجراء عمليات أخرى

🔒 **الأمان**:
- البيانات تُحفظ محلياً في المتصفح فقط
- لا يتم إرسال البيانات إلا عند الضغط على زر الإرسال
- يمكن تغيير رابط API في الكود حسب الحاجة

## التطوير والتخصيص

لتخصيص الإضافة:
1. عدل `content.js` لتغيير منطق الاستخراج
2. عدل `popup.html` لتغيير الواجهة
3. عدل `manifest.json` لإضافة صلاحيات جديدة

## الدعم

في حالة مواجهة مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من تحديث الإضافة
3. أعد تحميل صفحة نور وحاول مرة أخرى
