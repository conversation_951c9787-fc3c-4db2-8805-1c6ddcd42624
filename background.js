chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === "students") {
      console.log("📤 إرسال البيانات إلى السيرفر...");
  
      fetch("https://yoursite.com/api/import_students", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(message.data)
      })
      .then(res => res.json())
      .then(data => {
        console.log("✅ تم الحفظ بنجاح:", data);
      })
      .catch(err => {
        console.error("❌ خطأ أثناء الإرسال:", err);
      });
    }
  });
  