<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استخراج الجدول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background: #f2f2f2;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
        }
        .GridViewStyle {
            border: 2px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار استخراج بيانات الجدول</h1>
        
        <p>هذه الصفحة تحاكي جدول نور لاختبار وظيفة الاستخراج</p>
        
        <button onclick="testExtraction()">🔍 اختبار الاستخراج</button>
        <button onclick="addMoreRows()">➕ إضافة صفوف</button>
        <button onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <!-- محاكاة جدول نور -->
        <table id="ctl00_PlaceHolderMain_gvUserInfo" class="GridViewStyle">
            <tr>
                <th>اسم المستخدم</th>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>رقم الهوية</th>
                <th>الجنسية</th>
                <th>الصف</th>
                <th>الشعبة</th>
                <th>الفصل</th>
            </tr>
            <tr>
                <td>student001</td>
                <td>أحمد محمد علي السالم</td>
                <td><EMAIL></td>
                <td>1234567890</td>
                <td>سعودي</td>
                <td>الصف الأول الثانوي</td>
                <td>أ</td>
                <td>1/1</td>
            </tr>
            <tr>
                <td>student002</td>
                <td>فاطمة أحمد سالم الزهراني</td>
                <td><EMAIL></td>
                <td>1234567891</td>
                <td>سعودي</td>
                <td>الصف الأول الثانوي</td>
                <td>ب</td>
                <td>1/2</td>
            </tr>
            <tr>
                <td>student003</td>
                <td>محمد عبدالله حسن القحطاني</td>
                <td><EMAIL></td>
                <td>1234567892</td>
                <td>سعودي</td>
                <td>الصف الثاني الثانوي</td>
                <td>أ</td>
                <td>2/1</td>
            </tr>
            <tr>
                <td>student004</td>
                <td>نورا سعد محمد العتيبي</td>
                <td><EMAIL></td>
                <td>1234567893</td>
                <td>سعودي</td>
                <td>الصف الثاني الثانوي</td>
                <td>ب</td>
                <td>2/2</td>
            </tr>
            <tr>
                <td>student005</td>
                <td>خالد عبدالرحمن أحمد الغامدي</td>
                <td><EMAIL></td>
                <td>1234567894</td>
                <td>سعودي</td>
                <td>الصف الثالث الثانوي</td>
                <td>أ</td>
                <td>3/1</td>
            </tr>
        </table>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // نسخ وظيفة الاستخراج من content.js
        function extractStudentsFromTable() {
            let allStudents = [];
            
            // البحث عن الجدول بطرق متعددة
            let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
            
            if (!table) {
                table = document.querySelector("table[id*='gvUserInfo']");
            }
            
            if (!table) {
                table = document.querySelector("table.GridViewStyle");
            }
            
            if (!table) {
                // البحث عن أي جدول يحتوي على بيانات الطلاب
                const tables = document.querySelectorAll("table");
                for (const t of tables) {
                    const headers = t.querySelectorAll("th");
                    if (headers.length > 5) {
                        table = t;
                        break;
                    }
                }
            }
            
            if (!table) {
                console.warn("❌ لم يتم العثور على جدول البيانات");
                return [];
            }
            
            console.log("✅ تم العثور على الجدول:", table.id || table.className);
            
            const rows = table.querySelectorAll("tr") || [];
            let count = 0;

            rows.forEach((row) => {
                const cols = row.querySelectorAll("td");
                if (cols.length >= 5) {
                    const studentData = {
                        username: cols[0]?.innerText?.trim() || '',
                        name: cols[1]?.innerText?.trim() || '',
                        national_id: cols[3]?.innerText?.trim() || cols[2]?.innerText?.trim() || '',
                        grade: cols[5]?.innerText?.trim() || cols[4]?.innerText?.trim() || '',
                        class: cols[7]?.innerText?.trim() || cols[6]?.innerText?.trim() || cols[5]?.innerText?.trim() || ''
                    };
                    
                    // تجاهل الصفوف الفارغة أو صفوف العناوين
                    if (studentData.name && studentData.name !== 'الاسم' && studentData.name.length > 2) {
                        allStudents.push(studentData);
                        count++;
                    }
                }
            });

            console.log(`✅ تم استخراج ${count} طالبًا`);
            return allStudents;
        }

        function testExtraction() {
            const students = extractStudentsFromTable();
            const resultDiv = document.getElementById('result');
            
            if (students.length > 0) {
                let html = `<h3>✅ تم استخراج ${students.length} طالب بنجاح!</h3>`;
                html += '<table style="width: 100%; margin-top: 10px;">';
                html += '<tr><th>اسم المستخدم</th><th>الاسم</th><th>رقم الهوية</th><th>الصف</th><th>الفصل</th></tr>';
                
                students.forEach(s => {
                    html += `<tr>
                        <td>${s.username}</td>
                        <td>${s.name}</td>
                        <td>${s.national_id}</td>
                        <td>${s.grade}</td>
                        <td>${s.class}</td>
                    </tr>`;
                });
                
                html += '</table>';
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = '<h3>❌ لم يتم استخراج أي بيانات</h3>';
            }
            
            resultDiv.style.display = 'block';
        }

        function addMoreRows() {
            const table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
            const newRow = table.insertRow();
            const studentNum = table.rows.length - 1;
            
            newRow.innerHTML = `
                <td>student${String(studentNum).padStart(3, '0')}</td>
                <td>طالب تجريبي ${studentNum}</td>
                <td>test${studentNum}@example.com</td>
                <td>123456789${studentNum}</td>
                <td>سعودي</td>
                <td>الصف الأول الثانوي</td>
                <td>أ</td>
                <td>1/1</td>
            `;
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(testExtraction, 1000);
        });
    </script>
</body>
</html>
