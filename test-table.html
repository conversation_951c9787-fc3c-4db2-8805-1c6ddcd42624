<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استخراج الجدول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background: #f2f2f2;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
        }
        .GridViewStyle {
            border: 2px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار استخراج بيانات الجدول</h1>
        
        <p>هذه الصفحة تحاكي جدول نور لاختبار وظيفة الاستخراج</p>
        
        <button onclick="testExtraction()">🔍 اختبار الاستخراج</button>
        <button onclick="addMoreRows()">➕ إضافة صفوف</button>
        <button onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <!-- محاكاة جدول نور الحقيقي -->
        <table class="GridClass" cellspacing="0" rules="all" border="1" id="ctl00_PlaceHolderMain_gvUserInfo" style="border-color:BurlyWood;width:100%;border-collapse:collapse;">
            <tbody>
                <tr class="tableHeaderCell" align="right">
                    <th scope="col">اسم المستخدم</th>
                    <th scope="col">الاسم الرباعي</th>
                    <th scope="col">الاسم بالإنجليزية</th>
                    <th scope="col">رقم السجل المدني</th>
                    <th scope="col">النظام الدراسي</th>
                    <th scope="col">الصف</th>
                    <th scope="col">القسم</th>
                    <th scope="col">الفصل</th>
                    <th scope="col">حالة القيد</th>
                    <th scope="col">التدقيق</th>
                    <th scope="col">حالة السجل</th>
                </tr>
                <tr class="StandardFontPlain" align="right">
                    <td><a href="#">eb1122</a></td>
                    <td>ابراهيم تركي ابراهيم الشدوخي</td>
                    <td>IBRAHIM TURKI IBRAHIM ALSHUDUKHI</td>
                    <td>1158167138</td>
                    <td>منتظم</td>
                    <td>الثاني المتوسط</td>
                    <td>قسم عام</td>
                    <td>1</td>
                    <td>مرفع</td>
                    <td>مدقق</td>
                    <td>مستمر في الدراسة</td>
                </tr>
                <tr class="StandardFontPlain" align="right">
                    <td><a href="#">2352078659</a></td>
                    <td>ابراهيم علاء بركات العبيد</td>
                    <td>IBRAHIM ALAA BARAKAT ALOBAID</td>
                    <td>2352078659</td>
                    <td>منتظم</td>
                    <td>الأول المتوسط</td>
                    <td>قسم عام</td>
                    <td>2</td>
                    <td>مستجد</td>
                    <td>مدقق</td>
                    <td>مستمر في الدراسة</td>
                </tr>
                <tr class="StandardFontPlain" align="right">
                    <td><a href="#">43AD1155617549</a></td>
                    <td>ابراهيم علي بن براهيم السعودى</td>
                    <td>IBRAHIM ALI I ALSAWIDI</td>
                    <td>1155617549</td>
                    <td>منتظم</td>
                    <td>الثاني المتوسط</td>
                    <td>قسم عام</td>
                    <td>2</td>
                    <td>مرفع</td>
                    <td>مدقق</td>
                    <td>مستمر في الدراسة</td>
                </tr>
                <tr class="MyPagerStyle" align="right">
                    <td colspan="11">
                        <table border="0">
                            <tbody>
                                <tr>
                                    <td><span class="StandardFont">1</span></td>
                                    <td><a href="javascript:__doPostBack('ctl00$PlaceHolderMain$gvUserInfo','Page$2')">2</a></td>
                                    <td><a href="javascript:__doPostBack('ctl00$PlaceHolderMain$gvUserInfo','Page$3')">3</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // نسخ وظيفة الاستخراج من content.js
        function extractStudentsFromTable() {
            let allStudents = [];
            
            // البحث عن الجدول بطرق متعددة
            let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
            
            if (!table) {
                table = document.querySelector("table[id*='gvUserInfo']");
            }
            
            if (!table) {
                table = document.querySelector("table.GridClass");
            }
            
            if (!table) {
                // البحث عن أي جدول يحتوي على بيانات الطلاب
                const tables = document.querySelectorAll("table");
                for (const t of tables) {
                    const headers = t.querySelectorAll("th");
                    if (headers.length > 5) {
                        table = t;
                        break;
                    }
                }
            }
            
            if (!table) {
                console.warn("❌ لم يتم العثور على جدول البيانات");
                return [];
            }
            
            console.log("✅ تم العثور على الجدول:", table.id || table.className);
            
            const rows = table.querySelectorAll("tr") || [];
            let count = 0;

            rows.forEach((row) => {
                const cols = row.querySelectorAll("td");

                // تجاهل صف التنقل بين الصفحات (MyPagerStyle)
                if (row.classList.contains('MyPagerStyle')) {
                    return;
                }

                // التأكد من وجود 11 عمود كما في الجدول الحقيقي
                if (cols.length >= 11) {
                    // استخراج اسم المستخدم من الرابط في العمود الأول
                    const usernameLink = cols[0].querySelector('a');
                    const username = usernameLink ? usernameLink.textContent.trim() : cols[0].textContent.trim();

                    const studentData = {
                        username: username,
                        name: cols[1]?.textContent?.trim() || '',
                        name_english: cols[2]?.textContent?.trim() || '',
                        national_id: cols[3]?.textContent?.trim() || '',
                        study_system: cols[4]?.textContent?.trim() || '',
                        grade: cols[5]?.textContent?.trim() || '',
                        department: cols[6]?.textContent?.trim() || '',
                        class: cols[7]?.textContent?.trim() || '',
                        enrollment_status: cols[8]?.textContent?.trim() || '',
                        verification: cols[9]?.textContent?.trim() || '',
                        record_status: cols[10]?.textContent?.trim() || ''
                    };

                    // تجاهل الصفوف الفارغة أو صفوف العناوين
                    if (studentData.name &&
                        studentData.name !== 'الاسم الرباعي' &&
                        studentData.name.length > 2 &&
                        studentData.national_id &&
                        studentData.national_id !== 'رقم السجل المدني') {
                        allStudents.push(studentData);
                        count++;
                    }
                }
            });

            console.log(`✅ تم استخراج ${count} طالبًا`);
            return allStudents;
        }

        function testExtraction() {
            const students = extractStudentsFromTable();
            const resultDiv = document.getElementById('result');
            
            if (students.length > 0) {
                let html = `<h3>✅ تم استخراج ${students.length} طالب بنجاح!</h3>`;
                html += '<table style="width: 100%; margin-top: 10px;">';
                html += '<tr><th>اسم المستخدم</th><th>الاسم</th><th>رقم الهوية</th><th>الصف</th><th>القسم</th><th>الفصل</th><th>حالة القيد</th></tr>';

                students.forEach(s => {
                    html += `<tr>
                        <td>${s.username}</td>
                        <td>${s.name}</td>
                        <td>${s.national_id}</td>
                        <td>${s.grade}</td>
                        <td>${s.department}</td>
                        <td>${s.class}</td>
                        <td>${s.enrollment_status}</td>
                    </tr>`;
                });
                
                html += '</table>';
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = '<h3>❌ لم يتم استخراج أي بيانات</h3>';
            }
            
            resultDiv.style.display = 'block';
        }

        function addMoreRows() {
            const table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
            const newRow = table.insertRow();
            const studentNum = table.rows.length - 1;
            
            newRow.innerHTML = `
                <td>student${String(studentNum).padStart(3, '0')}</td>
                <td>طالب تجريبي ${studentNum}</td>
                <td>test${studentNum}@example.com</td>
                <td>123456789${studentNum}</td>
                <td>سعودي</td>
                <td>الصف الأول الثانوي</td>
                <td>أ</td>
                <td>1/1</td>
            `;
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(testExtraction, 1000);
        });
    </script>
</body>
</html>
