<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <title>بيانات الطلاب</title>
    <style>
      body {
        font-family: "Segoe UI", <PERSON><PERSON>a, Aria<PERSON>, sans-serif;
        padding: 15px;
        margin: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        min-width: 400px;
        max-width: 600px;
      }
      .container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      h3 {
        color: #2c3e50;
        margin-top: 0;
        text-align: center;
        border-bottom: 2px solid #3498db;
        padding-bottom: 10px;
      }
      .stats {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #3498db;
      }
      .stats h4 {
        margin: 0 0 10px 0;
        color: #2c3e50;
      }
      .stat-item {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        padding: 5px 0;
        border-bottom: 1px solid #ecf0f1;
      }
      .stat-item:last-child {
        border-bottom: none;
      }
      .stat-label {
        font-weight: bold;
        color: #34495e;
      }
      .stat-value {
        color: #27ae60;
        font-weight: bold;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        font-size: 12px;
        margin-top: 15px;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      th {
        background: #3498db;
        color: white;
        padding: 12px 8px;
        font-weight: bold;
        text-align: center;
      }
      td {
        border: 1px solid #ecf0f1;
        padding: 8px;
        text-align: center;
        transition: background-color 0.2s;
      }
      tr:nth-child(even) {
        background-color: #f8f9fa;
      }
      tr:hover {
        background-color: #e3f2fd;
      }
      .button-container {
        text-align: center;
        margin-top: 20px;
      }
      button {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        margin: 5px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      button:hover {
        background: linear-gradient(135deg, #2980b9, #1f5f8b);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
      button:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
      .loading {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
      }
      .no-data {
        text-align: center;
        padding: 40px;
        color: #e74c3c;
        background: #fdf2f2;
        border-radius: 8px;
        border: 1px solid #f5c6cb;
      }
      .table-container {
        max-height: 300px;
        overflow-y: auto;
        border-radius: 8px;
        border: 1px solid #ddd;
      }
      .refresh-btn {
        background: linear-gradient(135deg, #27ae60, #229954);
      }
      .refresh-btn:hover {
        background: linear-gradient(135deg, #229954, #1e8449);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h3>📋 بيانات الطلاب من نور</h3>

      <div id="statsSection" style="display: none">
        <div class="stats">
          <h4>📊 إحصائيات البيانات</h4>
          <div class="stat-item">
            <span class="stat-label">إجمالي الطلاب:</span>
            <span class="stat-value" id="totalStudents">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">عدد الصفوف:</span>
            <span class="stat-value" id="totalGrades">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">عدد الفصول:</span>
            <span class="stat-value" id="totalClasses">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">آخر تحديث:</span>
            <span class="stat-value" id="lastUpdate">-</span>
          </div>
        </div>
      </div>

      <div id="studentsTable">
        <div class="loading">🔄 جاري التحميل...</div>
      </div>

      <div class="button-container">
        <button id="refreshBtn" class="refresh-btn">🔄 تحديث البيانات</button>
        <button id="debugBtn" style="background: linear-gradient(135deg, #f39c12, #e67e22);">🔍 فحص التخزين</button>
        <button id="sendBtn">🚀 إرسال إلى السيرفر</button>
        <button id="exportBtn">📥 تصدير Excel</button>
      </div>
    </div>

    <script>
      // وظائف مساعدة
      function formatDate(date) {
        return new Intl.DateTimeFormat('ar-SA', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }).format(date);
      }

      function calculateStats(studentsData) {
        if (!studentsData || studentsData.length === 0) return null;

        const grades = [...new Set(studentsData.map(s => s.grade))];
        const classes = [...new Set(studentsData.map(s => s.class))];

        return {
          totalStudents: studentsData.length,
          totalGrades: grades.length,
          totalClasses: classes.length,
          lastUpdate: formatDate(new Date())
        };
      }

      function displayStudentsData(studentsData) {
        const container = document.getElementById("studentsTable");
        const statsSection = document.getElementById("statsSection");

        console.log("🎨 بدء عرض البيانات...");
        console.log("🎨 البيانات المستلمة:", studentsData);
        console.log("🎨 نوع البيانات:", typeof studentsData);
        console.log("🎨 هل هي مصفوفة:", Array.isArray(studentsData));

        if (studentsData) {
          console.log("🎨 طول المصفوفة:", studentsData.length);
          if (studentsData.length > 0) {
            console.log("🎨 عينة من البيانات:", studentsData[0]);
          }
        }

        if (!studentsData || studentsData.length === 0) {
          console.log("❌ لا توجد بيانات للعرض");

          // فحص إذا كانت هناك عملية استخراج جارية
          chrome.storage.local.get(['isExtracting', 'isDataReady', 'totalStudentsCount'], (result) => {
            console.log("🔍 حالة الاستخراج:", result);

            if (result.isExtracting) {
              container.innerHTML = `
                <div class="no-data">
                  <h4>🔄 جاري استخراج البيانات...</h4>
                  <p>الإضافة تعمل في الخلفية، يرجى الانتظار</p>
                  <p><small>آخر فحص: ${formatDate(new Date())}</small></p>
                </div>
              `;
            } else if (result.isDataReady && result.totalStudentsCount > 0) {
              container.innerHTML = `
                <div class="no-data">
                  <h4>⚠️ مشكلة في عرض البيانات</h4>
                  <p>تم استخراج ${result.totalStudentsCount} طالب لكن لا يمكن عرضهم</p>
                  <p>اضغط "🔍 فحص التخزين" لتشخيص المشكلة</p>
                  <p><small>آخر فحص: ${formatDate(new Date())}</small></p>
                </div>
              `;
            } else {
              container.innerHTML = `
                <div class="no-data">
                  <h4>📭 لا توجد بيانات</h4>
                  <p>اذهب إلى نظام نور وأعد تحميل الصفحة</p>
                  <p>الإضافة ستعمل تلقائياً وتستخرج البيانات</p>
                  <p><small>آخر فحص: ${formatDate(new Date())}</small></p>
                </div>
              `;
            }
          });
          statsSection.style.display = 'none';
          return;
        }

        // عرض الإحصائيات
        const stats = calculateStats(studentsData);
        document.getElementById("totalStudents").textContent = stats.totalStudents;
        document.getElementById("totalGrades").textContent = stats.totalGrades;
        document.getElementById("totalClasses").textContent = stats.totalClasses;
        document.getElementById("lastUpdate").textContent = stats.lastUpdate;
        statsSection.style.display = 'block';

        // عرض الجدول
        let html = `
          <div class="table-container">
            <table>
              <tr>
                <th>اسم المستخدم</th>
                <th>الاسم</th>
                <th>رقم الهوية</th>
                <th>الصف</th>
                <th>القسم</th>
                <th>الفصل</th>
                <th>حالة القيد</th>
              </tr>
        `;

        for (const s of studentsData) {
          html += `
            <tr>
              <td>${s.username || '-'}</td>
              <td>${s.name || '-'}</td>
              <td>${s.national_id || '-'}</td>
              <td>${s.grade || '-'}</td>
              <td>${s.department || '-'}</td>
              <td>${s.class || '-'}</td>
              <td>${s.enrollment_status || '-'}</td>
            </tr>
          `;
        }

        html += `
            </table>
          </div>
        `;
        container.innerHTML = html;
      }

      // تحميل البيانات عند فتح النافذة
      function loadData() {
        console.log('🔄 بدء تحميل البيانات من التخزين...');

        chrome.storage.local.get(null, (result) => {
          console.log("📦 جميع البيانات المحفوظة:", result);

          // البحث عن البيانات في المفاتيح المختلفة
          let studentsData = result.studentsData;
          let extractionInfo = result.extractionInfo;

          // إذا لم توجد البيانات، تحقق من جميع المفاتيح
          if (!studentsData) {
            console.log("🔍 البحث في جميع المفاتيح...");
            for (const key in result) {
              console.log(`🔍 فحص المفتاح: ${key}`, typeof result[key], Array.isArray(result[key]));
              if (Array.isArray(result[key]) && result[key].length > 0) {
                console.log(`✅ وجدت بيانات في المفتاح: ${key} (${result[key].length} عنصر)`);
                studentsData = result[key];
                break;
              }
            }
          }

          console.log("📊 البيانات النهائية للعرض:", studentsData);
          console.log("📊 نوع البيانات:", typeof studentsData);
          console.log("📊 هل هي مصفوفة:", Array.isArray(studentsData));

          if (studentsData && Array.isArray(studentsData)) {
            console.log(`✅ سيتم عرض ${studentsData.length} طالب`);
          } else {
            console.log("❌ البيانات غير صحيحة أو فارغة");
          }

          displayStudentsData(studentsData);

          // عرض معلومات الاستخراج إذا كانت متوفرة
          if (extractionInfo) {
            document.getElementById("lastUpdate").textContent = extractionInfo.extractionTime || formatDate(new Date());
          }
        });
      }

      // تصدير البيانات إلى Excel
      function exportToExcel(studentsData) {
        if (!studentsData || studentsData.length === 0) {
          alert("❌ لا توجد بيانات للتصدير");
          return;
        }

        let csvContent = "اسم المستخدم,الاسم,الاسم بالإنجليزية,رقم الهوية,النظام الدراسي,الصف,القسم,الفصل,حالة القيد,التدقيق,حالة السجل\n";
        studentsData.forEach(s => {
          csvContent += `"${s.username || ''}","${s.name || ''}","${s.name_english || ''}","${s.national_id || ''}","${s.study_system || ''}","${s.grade || ''}","${s.department || ''}","${s.class || ''}","${s.enrollment_status || ''}","${s.verification || ''}","${s.record_status || ''}"\n`;
        });

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `noor_students_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert(`✅ تم تصدير ${studentsData.length} طالب بنجاح!`);
      }

      // مراقبة تغييرات التخزين
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.onChanged.addListener((changes, namespace) => {
          console.log("🔄 تم تحديث التخزين:", changes);
          if (changes.studentsData || changes.extractionInfo) {
            loadData();
          }
        });
      }

      // مستقبل الرسائل من content script
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
          if (message.action === 'dataExtracted') {
            console.log(`📢 تم استلام إشعار: تم استخراج ${message.studentsCount} طالب من ${message.totalPages} صفحة`);

            // تحديث البيانات فوراً
            setTimeout(loadData, 500);

            sendResponse({status: 'received'});
          }
        });
      }

      // الأحداث
      document.addEventListener('DOMContentLoaded', () => {
        console.log("🚀 تم تحميل popup.html");
        loadData();

        // تحديث دوري كل 5 ثوان للتأكد من ظهور البيانات
        setInterval(() => {
          console.log("🔄 تحديث دوري للبيانات...");
          loadData();
        }, 5000);

        // زر التحديث
        const refreshBtn = document.getElementById("refreshBtn");
        if (refreshBtn) {
          refreshBtn.addEventListener("click", () => {
            console.log("🔄 تم الضغط على زر التحديث");
            loadData();
          });
        } else {
          console.error("❌ لم يتم العثور على زر التحديث");
        }

        // زر فحص التخزين
        const debugBtn = document.getElementById("debugBtn");
        if (debugBtn) {
          debugBtn.addEventListener("click", () => {
            console.log("🔍 تم الضغط على زر فحص التخزين");
            chrome.storage.local.get(null, (result) => {
              console.log("🔍 جميع البيانات المحفوظة:", result);

              let debugInfo = "🔍 معلومات التخزين:\n\n";

              for (const key in result) {
                const value = result[key];
                if (Array.isArray(value)) {
                  debugInfo += `📋 ${key}: ${value.length} عنصر\n`;
                  if (value.length > 0) {
                    debugInfo += `   عينة: ${JSON.stringify(value[0]).substring(0, 100)}...\n`;
                  }
                } else if (typeof value === 'object' && value !== null) {
                  debugInfo += `📦 ${key}: كائن (${Object.keys(value).length} خاصية)\n`;
                } else {
                  debugInfo += `📄 ${key}: ${typeof value} - ${String(value).substring(0, 50)}\n`;
                }
              }

              if (Object.keys(result).length === 0) {
                debugInfo += "❌ لا توجد بيانات محفوظة";
              }

              alert(debugInfo);
            });
          });
        } else {
          console.error("❌ لم يتم العثور على زر فحص التخزين");
        }

        // زر التصدير
        const exportBtn = document.getElementById("exportBtn");
        if (exportBtn) {
          exportBtn.addEventListener("click", () => {
            console.log("📥 تم الضغط على زر التصدير");
            chrome.storage.local.get(null, (result) => {
              // البحث عن البيانات في جميع المفاتيح
              let studentsData = result.studentsData;

              if (!studentsData) {
                for (const key in result) {
                  if (Array.isArray(result[key]) && result[key].length > 0) {
                    console.log(`📥 استخدام بيانات من المفتاح: ${key}`);
                    studentsData = result[key];
                    break;
                  }
                }
              }

              console.log("📥 البيانات للتصدير:", studentsData);
              exportToExcel(studentsData);
            });
          });
        } else {
          console.error("❌ لم يتم العثور على زر التصدير");
        }

        // زر الإرسال
        const sendBtn = document.getElementById("sendBtn");
        if (sendBtn) {
          sendBtn.addEventListener("click", () => {
            console.log("🚀 تم الضغط على زر الإرسال");
            chrome.storage.local.get(null, (result) => {
              // البحث عن البيانات في جميع المفاتيح
              let studentsData = result.studentsData || result.allStudents;

              if (!studentsData && result.noorStudentsData) {
                studentsData = result.noorStudentsData.studentsData;
              }

              console.log("🚀 البيانات للإرسال:", studentsData);

              if (!studentsData || studentsData.length === 0) {
                alert("❌ لا توجد بيانات لإرسالها");
                return;
              }

            const sendBtn = document.getElementById("sendBtn");
            sendBtn.disabled = true;
            sendBtn.textContent = "🔄 جاري الإرسال...";

            // محاكاة الإرسال للاختبار
            setTimeout(() => {
              alert("✅ تم إرسال البيانات بنجاح (محاكاة)");
              console.log("✅ البيانات المرسلة:", {
                students: studentsData,
                timestamp: new Date().toISOString(),
                total_count: studentsData.length
              });

              sendBtn.disabled = false;
              sendBtn.textContent = "🚀 إرسال إلى السيرفر";
            }, 2000);

            /*
            // كود الإرسال الحقيقي - قم بإلغاء التعليق عند الحاجة
            fetch("https://yoursite.com/api/import_students", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                students: studentsData,
                timestamp: new Date().toISOString(),
                total_count: studentsData.length
              })
            })
            .then(res => {
              if (!res.ok) throw new Error(`HTTP ${res.status}`);
              return res.json();
            })
            .then(data => {
              alert("✅ تم إرسال البيانات بنجاح");
              console.log("✅ الرد من السيرفر:", data);
            })
            .catch(err => {
              alert("❌ فشل في إرسال البيانات: " + err.message);
              console.error("خطأ:", err);
            })
            .finally(() => {
              sendBtn.disabled = false;
              sendBtn.textContent = "🚀 إرسال إلى السيرفر";
            });
            */
            });
          });
        } else {
          console.error("❌ لم يتم العثور على زر الإرسال");
        }
      });
    </script>
  </body>
</html>
