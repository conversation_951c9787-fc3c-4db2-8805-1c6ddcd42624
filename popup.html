<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <title>بيانات الطلاب</title>
    <style>
      body {
        font-family: "Segoe UI", <PERSON><PERSON>a, Aria<PERSON>, sans-serif;
        padding: 15px;
        margin: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        min-width: 400px;
        max-width: 600px;
      }
      .container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      h3 {
        color: #2c3e50;
        margin-top: 0;
        text-align: center;
        border-bottom: 2px solid #3498db;
        padding-bottom: 10px;
      }
      .stats {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #3498db;
      }
      .stats h4 {
        margin: 0 0 10px 0;
        color: #2c3e50;
      }
      .stat-item {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        padding: 5px 0;
        border-bottom: 1px solid #ecf0f1;
      }
      .stat-item:last-child {
        border-bottom: none;
      }
      .stat-label {
        font-weight: bold;
        color: #34495e;
      }
      .stat-value {
        color: #27ae60;
        font-weight: bold;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        font-size: 12px;
        margin-top: 15px;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      th {
        background: #3498db;
        color: white;
        padding: 12px 8px;
        font-weight: bold;
        text-align: center;
      }
      td {
        border: 1px solid #ecf0f1;
        padding: 8px;
        text-align: center;
        transition: background-color 0.2s;
      }
      tr:nth-child(even) {
        background-color: #f8f9fa;
      }
      tr:hover {
        background-color: #e3f2fd;
      }
      .button-container {
        text-align: center;
        margin-top: 20px;
      }
      button {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        margin: 5px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      button:hover {
        background: linear-gradient(135deg, #2980b9, #1f5f8b);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
      button:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
      .loading {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
      }
      .no-data {
        text-align: center;
        padding: 40px;
        color: #e74c3c;
        background: #fdf2f2;
        border-radius: 8px;
        border: 1px solid #f5c6cb;
      }
      .table-container {
        max-height: 300px;
        overflow-y: auto;
        border-radius: 8px;
        border: 1px solid #ddd;
      }
      .refresh-btn {
        background: linear-gradient(135deg, #27ae60, #229954);
      }
      .refresh-btn:hover {
        background: linear-gradient(135deg, #229954, #1e8449);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h3>📋 بيانات الطلاب من نور</h3>

      <div id="statsSection" style="display: none">
        <div class="stats">
          <h4>📊 إحصائيات البيانات</h4>
          <div class="stat-item">
            <span class="stat-label">إجمالي الطلاب:</span>
            <span class="stat-value" id="totalStudents">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">عدد الصفوف:</span>
            <span class="stat-value" id="totalGrades">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">عدد الفصول:</span>
            <span class="stat-value" id="totalClasses">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">آخر تحديث:</span>
            <span class="stat-value" id="lastUpdate">-</span>
          </div>
        </div>
      </div>

      <div id="studentsTable">
        <div class="loading">🔄 جاري التحميل...</div>
      </div>

      <div class="button-container">
        <button id="refreshBtn" class="refresh-btn">🔄 تحديث البيانات</button>
        <button id="debugBtn" style="background: linear-gradient(135deg, #f39c12, #e67e22);">🔍 فحص التخزين</button>
        <button id="sendBtn">🚀 إرسال إلى السيرفر</button>
        <button id="exportBtn">📥 تصدير Excel</button>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>

  </body>
</html>
