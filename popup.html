<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>بيانات الطلاب</title>
  <style>
    body { font-family: Arial; padding: 10px; }
    table { border-collapse: collapse; width: 100%; font-size: 14px; }
    th, td { border: 1px solid #ccc; padding: 5px; text-align: center; }
    button { margin-top: 10px; }
  </style>
</head>
<body>
  <h3>📋 بيانات الطلاب من نور</h3>
  <div id="studentsTable">جاري التحميل...</div>
  <button id="sendBtn">🚀 إرسال إلى السيرفر</button>

  <script>
    chrome.storage.local.get("studentsData", ({ studentsData }) => {
      const container = document.getElementById("studentsTable");
      if (!studentsData || studentsData.length === 0) {
        container.innerText = "لا توجد بيانات بعد. اذهب لنور واضغط على 'بحث'.";
        return;
      }

      let html = `<table><tr><th>الاسم</th><th>الهوية</th><th>الصف</th><th>الفصل</th></tr>`;
      for (const s of studentsData) {
        html += `<tr><td>${s.name}</td><td>${s.national_id}</td><td>${s.grade}</td><td>${s.class}</td></tr>`;
      }
      html += `</table>`;
      container.innerHTML = html;
    });

    document.getElementById("sendBtn").addEventListener("click", () => {
      chrome.storage.local.get("studentsData", ({ studentsData }) => {
        if (!studentsData) return alert("لا توجد بيانات لإرسالها");

        fetch("https://yoursite.com/api/import_students", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(studentsData)
        })
        .then(res => res.json())
        .then(data => {
          alert("✅ تم الإرسال بنجاح");
          console.log("✅ الرد من السيرفر:", data);
        })
        .catch(err => {
          alert("❌ فشل الإرسال");
          console.error("خطأ:", err);
        });
      });
    });
  </script>
</body>
</html>
