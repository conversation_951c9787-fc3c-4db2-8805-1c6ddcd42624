<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <title>بيانات الطلاب</title>
    <style>
      body {
        font-family: "Segoe UI", <PERSON><PERSON>a, Aria<PERSON>, sans-serif;
        padding: 15px;
        margin: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        min-width: 400px;
        max-width: 600px;
      }
      .container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      h3 {
        color: #2c3e50;
        margin-top: 0;
        text-align: center;
        border-bottom: 2px solid #3498db;
        padding-bottom: 10px;
      }
      .stats {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #3498db;
      }
      .stats h4 {
        margin: 0 0 10px 0;
        color: #2c3e50;
      }
      .stat-item {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        padding: 5px 0;
        border-bottom: 1px solid #ecf0f1;
      }
      .stat-item:last-child {
        border-bottom: none;
      }
      .stat-label {
        font-weight: bold;
        color: #34495e;
      }
      .stat-value {
        color: #27ae60;
        font-weight: bold;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        font-size: 12px;
        margin-top: 15px;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      th {
        background: #3498db;
        color: white;
        padding: 12px 8px;
        font-weight: bold;
        text-align: center;
      }
      td {
        border: 1px solid #ecf0f1;
        padding: 8px;
        text-align: center;
        transition: background-color 0.2s;
      }
      tr:nth-child(even) {
        background-color: #f8f9fa;
      }
      tr:hover {
        background-color: #e3f2fd;
      }
      .button-container {
        text-align: center;
        margin-top: 20px;
      }
      button {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        margin: 5px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      button:hover {
        background: linear-gradient(135deg, #2980b9, #1f5f8b);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
      button:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
      .loading {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
      }
      .no-data {
        text-align: center;
        padding: 40px;
        color: #e74c3c;
        background: #fdf2f2;
        border-radius: 8px;
        border: 1px solid #f5c6cb;
      }
      .table-container {
        max-height: 300px;
        overflow-y: auto;
        border-radius: 8px;
        border: 1px solid #ddd;
      }
      .refresh-btn {
        background: linear-gradient(135deg, #27ae60, #229954);
      }
      .refresh-btn:hover {
        background: linear-gradient(135deg, #229954, #1e8449);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h3>📋 بيانات الطلاب من نور</h3>

      <div id="statsSection" style="display: none">
        <div class="stats">
          <h4>📊 إحصائيات البيانات</h4>
          <div class="stat-item">
            <span class="stat-label">إجمالي الطلاب:</span>
            <span class="stat-value" id="totalStudents">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">عدد الصفوف:</span>
            <span class="stat-value" id="totalGrades">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">عدد الفصول:</span>
            <span class="stat-value" id="totalClasses">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">آخر تحديث:</span>
            <span class="stat-value" id="lastUpdate">-</span>
          </div>
        </div>
      </div>

      <div id="studentsTable">
        <div class="loading">🔄 جاري التحميل...</div>
      </div>

      <div class="button-container">
        <button id="refreshBtn" class="refresh-btn">🔄 تحديث البيانات</button>
        <button id="debugBtn" style="background: linear-gradient(135deg, #f39c12, #e67e22);">🔍 فحص التخزين</button>
        <button id="sendBtn">🚀 إرسال إلى السيرفر</button>
        <button id="exportBtn">📥 تصدير Excel</button>
      </div>
    </div>

    <script>
      // وظائف مساعدة
      function formatDate(date) {
        return new Intl.DateTimeFormat('ar-SA', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }).format(date);
      }

      function calculateStats(studentsData) {
        if (!studentsData || studentsData.length === 0) return null;

        const grades = [...new Set(studentsData.map(s => s.grade))];
        const classes = [...new Set(studentsData.map(s => s.class))];

        return {
          totalStudents: studentsData.length,
          totalGrades: grades.length,
          totalClasses: classes.length,
          lastUpdate: formatDate(new Date())
        };
      }

      function displayStudentsData(studentsData) {
        const container = document.getElementById("studentsTable");
        const statsSection = document.getElementById("statsSection");

        console.log("🎨 عرض البيانات:", studentsData ? studentsData.length : 0, "طالب");

        if (!studentsData || studentsData.length === 0) {
          container.innerHTML = `
            <div class="no-data">
              <h4>📭 لا توجد بيانات</h4>
              <p>اذهب إلى نظام نور وأعد تحميل الصفحة</p>
              <p>الإضافة ستعمل تلقائياً وتستخرج البيانات</p>
            </div>
          `;
          statsSection.style.display = 'none';
          return;
        }

        // عرض الإحصائيات
        const stats = calculateStats(studentsData);
        document.getElementById("totalStudents").textContent = stats.totalStudents;
        document.getElementById("totalGrades").textContent = stats.totalGrades;
        document.getElementById("totalClasses").textContent = stats.totalClasses;
        document.getElementById("lastUpdate").textContent = stats.lastUpdate;
        statsSection.style.display = 'block';

        // عرض الجدول
        let html = `
          <div class="table-container">
            <table>
              <tr>
                <th>اسم المستخدم</th>
                <th>الاسم</th>
                <th>رقم الهوية</th>
                <th>الصف</th>
                <th>القسم</th>
                <th>الفصل</th>
                <th>حالة القيد</th>
              </tr>
        `;

        for (const s of studentsData) {
          html += `
            <tr>
              <td>${s.username || '-'}</td>
              <td>${s.name || '-'}</td>
              <td>${s.national_id || '-'}</td>
              <td>${s.grade || '-'}</td>
              <td>${s.department || '-'}</td>
              <td>${s.class || '-'}</td>
              <td>${s.enrollment_status || '-'}</td>
            </tr>
          `;
        }

        html += `
            </table>
          </div>
        `;
        container.innerHTML = html;
      }

      // تحميل البيانات عند فتح النافذة
      function loadData() {
        chrome.storage.local.get(null, (result) => {
          console.log("📦 البيانات المحفوظة:", result);

          // البحث عن البيانات بطريقة مباشرة
          let studentsData = null;

          // جرب جميع المفاتيح المحتملة
          if (result.studentsData && Array.isArray(result.studentsData)) {
            studentsData = result.studentsData;
            console.log("✅ وجدت البيانات في studentsData:", studentsData.length);
          } else if (result.allStudents && Array.isArray(result.allStudents)) {
            studentsData = result.allStudents;
            console.log("✅ وجدت البيانات في allStudents:", studentsData.length);
          } else if (result.noorStudentsData && result.noorStudentsData.studentsData) {
            studentsData = result.noorStudentsData.studentsData;
            console.log("✅ وجدت البيانات في noorStudentsData:", studentsData.length);
          }

          // عرض البيانات مباشرة
          if (studentsData && studentsData.length > 0) {
            console.log("✅ عرض البيانات:", studentsData.length, "طالب");
            displayStudentsData(studentsData);
          } else {
            console.log("❌ لا توجد بيانات للعرض");
            displayStudentsData([]);
          }
        });
      }

      // تصدير البيانات إلى Excel
      function exportToExcel(studentsData) {
        if (!studentsData || studentsData.length === 0) {
          alert("❌ لا توجد بيانات للتصدير");
          return;
        }

        let csvContent = "اسم المستخدم,الاسم,الاسم بالإنجليزية,رقم الهوية,النظام الدراسي,الصف,القسم,الفصل,حالة القيد,التدقيق,حالة السجل\n";
        studentsData.forEach(s => {
          csvContent += `"${s.username || ''}","${s.name || ''}","${s.name_english || ''}","${s.national_id || ''}","${s.study_system || ''}","${s.grade || ''}","${s.department || ''}","${s.class || ''}","${s.enrollment_status || ''}","${s.verification || ''}","${s.record_status || ''}"\n`;
        });

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `noor_students_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert(`✅ تم تصدير ${studentsData.length} طالب بنجاح!`);
      }

      // مراقبة تغييرات التخزين
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.onChanged.addListener((changes, namespace) => {
          console.log("🔄 تم تحديث التخزين:", changes);
          if (changes.studentsData || changes.extractionInfo) {
            loadData();
          }
        });
      }

      // مستقبل الرسائل من content script
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
          if (message.action === 'dataExtracted') {
            console.log(`📢 تم استلام إشعار: تم استخراج ${message.studentsCount} طالب من ${message.totalPages} صفحة`);

            // تحديث البيانات فوراً
            setTimeout(loadData, 500);

            sendResponse({status: 'received'});
          }
        });
      }

      // الأحداث
      document.addEventListener('DOMContentLoaded', () => {
        loadData();

        // تحديث كل 3 ثوان
        setInterval(loadData, 3000);

        // زر التحديث
        document.getElementById("refreshBtn").addEventListener("click", loadData);

        // زر فحص التخزين
        document.getElementById("debugBtn").addEventListener("click", () => {
          chrome.storage.local.get(null, (result) => {
            let info = "البيانات المحفوظة:\n\n";
            for (const key in result) {
              if (Array.isArray(result[key])) {
                info += `${key}: ${result[key].length} عنصر\n`;
              } else {
                info += `${key}: ${typeof result[key]}\n`;
              }
            }
            alert(info || "لا توجد بيانات");
          });
        });

        // زر التصدير
        document.getElementById("exportBtn").addEventListener("click", () => {
          chrome.storage.local.get(null, (result) => {
            let studentsData = result.studentsData || result.allStudents;
            if (!studentsData && result.noorStudentsData) {
              studentsData = result.noorStudentsData.studentsData;
            }
            exportToExcel(studentsData);
          });
        });

        // زر الإرسال
        document.getElementById("sendBtn").addEventListener("click", () => {
          chrome.storage.local.get(null, (result) => {
            let studentsData = result.studentsData || result.allStudents;
            if (!studentsData && result.noorStudentsData) {
              studentsData = result.noorStudentsData.studentsData;
            }

            if (!studentsData || studentsData.length === 0) {
              alert("❌ لا توجد بيانات لإرسالها");
              return;
            }

            const sendBtn = document.getElementById("sendBtn");
            sendBtn.disabled = true;
            sendBtn.textContent = "🔄 جاري الإرسال...";

            // محاكاة الإرسال للاختبار
            setTimeout(() => {
              alert("✅ تم إرسال البيانات بنجاح (محاكاة)");
              console.log("✅ البيانات المرسلة:", {
                students: studentsData,
                timestamp: new Date().toISOString(),
                total_count: studentsData.length
              });

              sendBtn.disabled = false;
              sendBtn.textContent = "🚀 إرسال إلى السيرفر";
            }, 2000);

            /*
            // كود الإرسال الحقيقي - قم بإلغاء التعليق عند الحاجة
            fetch("https://yoursite.com/api/import_students", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                students: studentsData,
                timestamp: new Date().toISOString(),
                total_count: studentsData.length
              })
            })
            .then(res => {
              if (!res.ok) throw new Error(`HTTP ${res.status}`);
              return res.json();
            })
            .then(data => {
              alert("✅ تم إرسال البيانات بنجاح");
              console.log("✅ الرد من السيرفر:", data);
            })
            .catch(err => {
              alert("❌ فشل في إرسال البيانات: " + err.message);
              console.error("خطأ:", err);
            })
            .finally(() => {
              sendBtn.disabled = false;
              sendBtn.textContent = "🚀 إرسال إلى السيرفر";
            });
            */
          });
        });
      });
    </script>
  </body>
</html>
