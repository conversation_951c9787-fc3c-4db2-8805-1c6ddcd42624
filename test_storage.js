// ملف اختبار سريع لتشخيص مشكلة التخزين
// يمكن تشغيله في console لفحص البيانات

console.log("🔍 بدء فحص التخزين...");

// فحص جميع البيانات المحفوظة
chrome.storage.local.get(null, (result) => {
  console.log("📦 جميع البيانات المحفوظة:", result);
  
  console.log("🔍 تحليل البيانات:");
  
  for (const key in result) {
    const value = result[key];
    console.log(`\n📋 المفتاح: ${key}`);
    console.log(`   النوع: ${typeof value}`);
    
    if (Array.isArray(value)) {
      console.log(`   مصفوفة بحجم: ${value.length}`);
      if (value.length > 0) {
        console.log(`   عينة من البيانات:`, value[0]);
      }
    } else if (typeof value === 'object' && value !== null) {
      console.log(`   كائن بخصائص: ${Object.keys(value).length}`);
      console.log(`   الخصائص:`, Object.keys(value));
      
      if (value.studentsData && Array.isArray(value.studentsData)) {
        console.log(`   يحتوي على studentsData: ${value.studentsData.length} طالب`);
      }
    } else {
      console.log(`   القيمة: ${String(value).substring(0, 100)}`);
    }
  }
  
  // البحث عن بيانات الطلاب
  console.log("\n🎯 البحث عن بيانات الطلاب:");
  
  let foundData = [];
  
  if (result.studentsData && Array.isArray(result.studentsData)) {
    foundData.push({ key: 'studentsData', count: result.studentsData.length });
  }
  
  if (result.allStudents && Array.isArray(result.allStudents)) {
    foundData.push({ key: 'allStudents', count: result.allStudents.length });
  }
  
  if (result.noorStudentsData && result.noorStudentsData.studentsData) {
    foundData.push({ key: 'noorStudentsData.studentsData', count: result.noorStudentsData.studentsData.length });
  }
  
  if (foundData.length > 0) {
    console.log("✅ تم العثور على بيانات الطلاب:");
    foundData.forEach(data => {
      console.log(`   ${data.key}: ${data.count} طالب`);
    });
  } else {
    console.log("❌ لم يتم العثور على أي بيانات طلاب");
  }
  
  // اختبار popup
  console.log("\n🎨 اختبار منطق popup:");
  
  let studentsData = result.studentsData;
  
  if (!studentsData) {
    for (const key in result) {
      if (Array.isArray(result[key]) && result[key].length > 0) {
        console.log(`🔍 استخدام بيانات من المفتاح: ${key}`);
        studentsData = result[key];
        break;
      }
    }
  }
  
  if (studentsData && Array.isArray(studentsData)) {
    console.log(`✅ popup سيعرض ${studentsData.length} طالب`);
    console.log(`📝 عينة من الطالب الأول:`, studentsData[0]);
  } else {
    console.log("❌ popup لن يعرض أي بيانات");
  }
});

// اختبار حفظ بيانات تجريبية
console.log("\n🧪 اختبار حفظ بيانات تجريبية...");

const testData = [
  {
    username: "test123",
    name: "طالب تجريبي",
    name_english: "Test Student",
    national_id: "1234567890",
    study_system: "منتظم",
    grade: "الأول المتوسط",
    department: "قسم عام",
    class: "1",
    enrollment_status: "مرفع",
    verification: "مدقق",
    record_status: "مستمر في الدراسة"
  }
];

chrome.storage.local.set({
  testStudentsData: testData,
  testTimestamp: new Date().toISOString()
}, () => {
  console.log("✅ تم حفظ البيانات التجريبية");
  
  // التحقق من الحفظ
  chrome.storage.local.get(['testStudentsData'], (result) => {
    if (result.testStudentsData && result.testStudentsData.length > 0) {
      console.log("✅ تم التحقق من حفظ البيانات التجريبية بنجاح");
    } else {
      console.error("❌ فشل في حفظ البيانات التجريبية");
    }
  });
});

console.log("🔍 انتهى فحص التخزين");
