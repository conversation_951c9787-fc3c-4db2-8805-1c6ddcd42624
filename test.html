<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة نور</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 4px solid #3498db;
        }
        .success {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار إضافة جلب طلاب نور</h1>
        
        <div class="test-section">
            <h3>1. اختبار تخزين البيانات</h3>
            <p>اختبار حفظ واسترجاع بيانات الطلاب من التخزين المحلي</p>
            <button onclick="testStorage()">اختبار التخزين</button>
            <button onclick="clearStorage()">مسح البيانات</button>
            <div id="storageResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار عرض البيانات</h3>
            <p>اختبار عرض البيانات في جدول مع الإحصائيات</p>
            <button onclick="testDisplay()">اختبار العرض</button>
            <div id="displayResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار التصدير</h3>
            <p>اختبار تصدير البيانات إلى ملف CSV</p>
            <button onclick="testExport()">اختبار التصدير</button>
            <div id="exportResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. معلومات الإضافة</h3>
            <p>عرض معلومات حالة الإضافة</p>
            <button onclick="checkExtension()">فحص الإضافة</button>
            <div id="extensionResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // بيانات تجريبية
        const sampleStudents = [
            {
                username: "student001",
                name: "أحمد محمد علي",
                national_id: "1234567890",
                grade: "الصف الأول الثانوي",
                class: "1/1"
            },
            {
                username: "student002", 
                name: "فاطمة أحمد سالم",
                national_id: "1234567891",
                grade: "الصف الأول الثانوي",
                class: "1/2"
            },
            {
                username: "student003",
                name: "محمد عبدالله حسن",
                national_id: "1234567892", 
                grade: "الصف الثاني الثانوي",
                class: "2/1"
            }
        ];

        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = message;
        }

        function testStorage() {
            try {
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    const testData = {
                        studentsData: sampleStudents,
                        extractionInfo: {
                            totalStudents: sampleStudents.length,
                            totalPages: 1,
                            extractionDate: new Date().toISOString(),
                            extractionTime: new Date().toLocaleString('ar-SA'),
                            source: 'اختبار محلي'
                        }
                    };

                    chrome.storage.local.set(testData, () => {
                        chrome.storage.local.get(['studentsData', 'extractionInfo'], (result) => {
                            if (result.studentsData && result.studentsData.length > 0) {
                                showResult('storageResult', 
                                    `✅ تم حفظ واسترجاع ${result.studentsData.length} طالب بنجاح!<br>` +
                                    `📅 تاريخ الاستخراج: ${result.extractionInfo?.extractionTime || 'غير محدد'}`
                                );
                            } else {
                                showResult('storageResult', '❌ فشل في استرجاع البيانات', false);
                            }
                        });
                    });
                } else {
                    showResult('storageResult', '❌ Chrome storage API غير متوفر. تأكد من تشغيل الصفحة كإضافة.', false);
                }
            } catch (error) {
                showResult('storageResult', `❌ خطأ: ${error.message}`, false);
            }
        }

        function clearStorage() {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.clear(() => {
                    showResult('storageResult', '🗑️ تم مسح جميع البيانات المحفوظة');
                });
            } else {
                showResult('storageResult', '❌ Chrome storage API غير متوفر', false);
            }
        }

        function testDisplay() {
            const stats = {
                totalStudents: sampleStudents.length,
                totalGrades: [...new Set(sampleStudents.map(s => s.grade))].length,
                totalClasses: [...new Set(sampleStudents.map(s => s.class))].length
            };

            showResult('displayResult', 
                `📊 إحصائيات العرض:<br>` +
                `👥 إجمالي الطلاب: ${stats.totalStudents}<br>` +
                `📚 عدد الصفوف: ${stats.totalGrades}<br>` +
                `🏫 عدد الفصول: ${stats.totalClasses}`
            );
        }

        function testExport() {
            try {
                let csvContent = "اسم المستخدم,الاسم,رقم الهوية,الصف,الفصل\n";
                sampleStudents.forEach(s => {
                    csvContent += `"${s.username}","${s.name}","${s.national_id}","${s.grade}","${s.class}"\n`;
                });

                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                
                showResult('exportResult', 
                    `✅ تم إنشاء ملف CSV بنجاح!<br>` +
                    `📁 حجم الملف: ${blob.size} بايت<br>` +
                    `📄 عدد الأسطر: ${sampleStudents.length + 1}`
                );
            } catch (error) {
                showResult('exportResult', `❌ فشل في التصدير: ${error.message}`, false);
            }
        }

        function checkExtension() {
            let info = '🔍 معلومات الإضافة:<br>';
            
            if (typeof chrome !== 'undefined') {
                info += '✅ Chrome API متوفر<br>';
                
                if (chrome.storage) {
                    info += '✅ Storage API متوفر<br>';
                } else {
                    info += '❌ Storage API غير متوفر<br>';
                }
                
                if (chrome.runtime) {
                    info += '✅ Runtime API متوفر<br>';
                } else {
                    info += '❌ Runtime API غير متوفر<br>';
                }
            } else {
                info += '❌ Chrome API غير متوفر<br>';
            }
            
            info += `🌐 User Agent: ${navigator.userAgent.includes('Chrome') ? 'Chrome' : 'متصفح آخر'}<br>`;
            info += `📍 الموقع: ${location.href}`;
            
            showResult('extensionResult', info);
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(checkExtension, 1000);
        });
    </script>
</body>
</html>
