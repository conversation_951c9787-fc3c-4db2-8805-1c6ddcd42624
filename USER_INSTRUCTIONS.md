# تعليمات استخدام إضافة جلب طلاب نور

## 🎯 المشكلة التي تم حلها

بناءً على الرسالة التي ظهرت لك:
> "❌ لم يتم العثور على جدول البيانات"

تم تحديث الإضافة لتتعامل مع هذه المشكلة وترشدك خلال العملية.

## 📋 الخطوات الصحيحة للاستخدام

### 1. إعداد البحث في نور
1. **اذهب إلى صفحة البحث**: `https://noor.moe.gov.sa/Noor/UsersManagement/UserSearch.aspx`
2. **اختر معايير البحث**:
   - المدرسة
   - الصف الدراسي
   - الفصل (اختياري)
   - أي معايير أخرى مطلوبة

### 2. تشغيل الإضافة
1. **أعد تحميل الإضافة** في `chrome://extensions/`
2. **أعد تحميل صفحة نور** (F5)
3. **انتظر 3 ثوان** حتى تبدأ الإضافة

### 3. سيناريوهات مختلفة

#### أ. إذا لم تقم بالبحث بعد:
- ستظهر رسالة: "🔍 إضافة نور جاهزة!"
- **اختر معايير البحث واضغط "بحث"**
- ستبدأ الإضافة تلقائياً في الاستخراج

#### ب. إذا كانت النتائج موجودة:
- ستبدأ الإضافة مباشرة في استخراج البيانات
- راقب رسائل console (F12)

#### ج. إذا لم توجد نتائج:
- ستظهر رسالة تحذيرية
- **غيّر معايير البحث وحاول مرة أخرى**

## 🔍 مراقبة العملية

### افتح Developer Tools:
1. اضغط **F12**
2. انتقل إلى تبويب **Console**
3. راقب الرسائل:

```
🚀 بدء تشغيل إضافة نور...
📊 حالة الصفحة: search_ready
🔍 الصفحة جاهزة للبحث، مراقبة زر البحث...
```

### بعد الضغط على "بحث":
```
🔍 تم اكتشاف الضغط على زر البحث
🔄 بدء مراقبة ظهور النتائج...
✅ تم اكتشاف ظهور النتائج، بدء الاستخراج...
📝 طالب 1: أحمد محمد علي - ID: 1234567890
```

## ⚠️ رسائل التحذير والحلول

### "❌ لا يمكن استخراج البيانات"
**السبب**: لم يتم إجراء البحث أو لا توجد نتائج

**الحل**:
1. اضغط زر "بحث" في نور
2. تأكد من ظهور نتائج
3. أعد تشغيل الإضافة

### "🔍 يجب إجراء البحث أولاً"
**السبب**: الصفحة جاهزة لكن لم يتم البحث

**الحل**:
1. اختر معايير البحث
2. اضغط زر "بحث"
3. انتظر ظهور النتائج

### "⏰ انتهت مهلة انتظار النتائج"
**السبب**: البحث يستغرق وقت طويل أو فشل

**الحل**:
1. تحقق من الاتصال بالإنترنت
2. أعد تحميل الصفحة
3. حاول مرة أخرى

## 🎉 علامات النجاح

### ستعرف أن العملية نجحت عندما ترى:
```
✅ تم استخراج X طالبًا من الصفحة 1
📄 الانتقال إلى الصفحة 2...
✅ تم استخراج X طالبًا من الصفحة 2
...
✅ اكتمل استخراج X طالبًا من X صفحة
💾 تم حفظ جميع بيانات الطلاب في التخزين المحلي
```

### ستظهر رسالة تأكيد:
> "✅ تم استخراج X طالبًا بنجاح!
> 
> يمكنك الآن فتح نافذة الإضافة لعرض النتائج."

## 📊 عرض النتائج

### بعد نجاح الاستخراج:
1. **اضغط على أيقونة الإضافة** في شريط الأدوات
2. ستظهر نافذة تحتوي على:
   - إحصائيات البيانات
   - جدول بجميع الطلاب
   - أزرار للتصدير والإرسال

### الأزرار المتوفرة:
- **🔄 تحديث البيانات**: لإعادة تحميل البيانات
- **🔍 فحص التخزين**: لتشخيص المشاكل
- **📥 تصدير Excel**: لتحميل ملف CSV
- **🚀 إرسال إلى السيرفر**: لإرسال البيانات (محاكاة)

## 🛠️ استكشاف الأخطاء

### إذا لم تعمل الإضافة:
1. **تأكد من تحميل الصفحة بالكامل**
2. **أعد تحميل الإضافة** في chrome://extensions/
3. **أعد تحميل صفحة نور**
4. **تحقق من console للأخطاء**

### إذا لم تظهر النتائج في popup:
1. اضغط زر **"🔍 فحص التخزين"**
2. تحقق من وجود البيانات
3. اضغط زر **"🔄 تحديث البيانات"**

### إذا كانت النتائج فارغة:
1. **تأكد من وجود طلاب في البحث**
2. **جرب معايير بحث مختلفة**
3. **تحقق من الصلاحيات في نور**

## 📞 طلب المساعدة

إذا استمرت المشاكل، أرسل:
1. **لقطة شاشة** من صفحة نور
2. **رسائل console** (انسخ النص)
3. **وصف المشكلة** بالتفصيل

## ✅ نصائح للنجاح

1. **اتبع الخطوات بالترتيب**
2. **انتظر تحميل الصفحة بالكامل**
3. **تأكد من وجود نتائج للبحث**
4. **راقب رسائل console**
5. **لا تغلق نافذة نور أثناء الاستخراج**

---

**ملاحظة**: الإضافة الآن أذكى وتتفاعل مع حالة الصفحة تلقائياً. ما عليك سوى اتباع التعليمات التي تظهر لك!
