<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار popup الإضافة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار popup الإضافة</h1>
        
        <p>هذه الصفحة تساعد في اختبار popup.html بدون الحاجة لتثبيت الإضافة</p>
        
        <button onclick="addTestData()">إضافة بيانات تجريبية</button>
        <button onclick="clearData()">مسح البيانات</button>
        <button onclick="showPopup()">عرض popup</button>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <iframe id="popupFrame" style="display: none;" src="popup.html"></iframe>
    </div>

    <script>
        // محاكاة Chrome storage API
        if (typeof chrome === 'undefined') {
            window.chrome = {
                storage: {
                    local: {
                        data: {},
                        set: function(items, callback) {
                            Object.assign(this.data, items);
                            console.log("💾 تم حفظ البيانات:", items);
                            if (callback) callback();
                        },
                        get: function(keys, callback) {
                            if (keys === null) {
                                callback(this.data);
                            } else if (Array.isArray(keys)) {
                                const result = {};
                                keys.forEach(key => {
                                    if (this.data[key] !== undefined) {
                                        result[key] = this.data[key];
                                    }
                                });
                                callback(result);
                            } else if (typeof keys === 'string') {
                                const result = {};
                                if (this.data[keys] !== undefined) {
                                    result[keys] = this.data[keys];
                                }
                                callback(result);
                            } else if (typeof keys === 'object') {
                                const result = {};
                                for (const key in keys) {
                                    result[key] = this.data[key] !== undefined ? this.data[key] : keys[key];
                                }
                                callback(result);
                            }
                        },
                        clear: function(callback) {
                            this.data = {};
                            console.log("🗑️ تم مسح جميع البيانات");
                            if (callback) callback();
                        }
                    }
                }
            };
        }

        // بيانات تجريبية
        const sampleStudents = [
            {
                username: "student001",
                name: "أحمد محمد علي السالم",
                national_id: "1234567890",
                grade: "الصف الأول الثانوي",
                class: "1/1"
            },
            {
                username: "student002", 
                name: "فاطمة أحمد سالم الزهراني",
                national_id: "1234567891",
                grade: "الصف الأول الثانوي",
                class: "1/2"
            },
            {
                username: "student003",
                name: "محمد عبدالله حسن القحطاني",
                national_id: "1234567892", 
                grade: "الصف الثاني الثانوي",
                class: "2/1"
            },
            {
                username: "student004",
                name: "نورا سعد محمد العتيبي",
                national_id: "1234567893",
                grade: "الصف الثاني الثانوي", 
                class: "2/2"
            },
            {
                username: "student005",
                name: "خالد عبدالرحمن أحمد الغامدي",
                national_id: "1234567894",
                grade: "الصف الثالث الثانوي",
                class: "3/1"
            }
        ];

        function addTestData() {
            const testData = {
                studentsData: sampleStudents,
                extractionInfo: {
                    totalStudents: sampleStudents.length,
                    totalPages: 1,
                    extractionDate: new Date().toISOString(),
                    extractionTime: new Date().toLocaleString('ar-SA'),
                    source: 'بيانات تجريبية'
                }
            };

            chrome.storage.local.set(testData, () => {
                showResult(`✅ تم إضافة ${sampleStudents.length} طالب تجريبي بنجاح!`);
            });
        }

        function clearData() {
            chrome.storage.local.clear(() => {
                showResult("🗑️ تم مسح جميع البيانات");
            });
        }

        function showPopup() {
            const frame = document.getElementById('popupFrame');
            frame.style.display = 'block';
            frame.src = frame.src; // إعادة تحميل
            showResult("📋 تم عرض popup في الإطار أدناه");
        }

        function showResult(message) {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = message;
        }

        // إضافة بيانات تجريبية تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestData();
                showResult("🎯 تم تحميل بيانات تجريبية تلقائياً. اضغط 'عرض popup' لرؤية النتيجة.");
            }, 1000);
        });
    </script>
</body>
</html>
