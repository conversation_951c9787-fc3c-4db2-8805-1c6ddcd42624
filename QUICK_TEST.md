# دليل الاختبار السريع - إضافة جلب طلاب نور

## 🎯 التحديثات الجديدة

تم تحديث الكود ليتعامل مع **البنية الحقيقية** لجدول نور:

### 📋 بنية الجدول الحقيقية:
- **11 عمود**: اسم المستخدم، الاسم الرباعي، الاسم بالإنجليزية، رقم السجل المدني، النظام الدراسي، الصف، القسم، الفصل، حالة القيد، التدقيق، حالة السجل
- **Class**: `GridClass` (ليس `GridViewStyle`)
- **صف التنقل**: `tr.MyPagerStyle`
- **روابط الصفحات**: `javascript:__doPostBack('ctl00$PlaceHolderMain$gvUserInfo','Page$X')`

## 🧪 خطوات الاختبار

### 1. اختبار محلي أولاً:
```bash
# افتح test-table.html في المتصفح
open test-table.html
```

**النتيجة المتوقعة:**
- ✅ استخراج 3 طلاب من الجدول التجريبي
- ✅ عرض جميع البيانات (اسم المستخدم، الاسم، رقم الهوية، الصف، القسم، الفصل، حالة القيد)

### 2. اختبار مع نور:

#### أ. إعادة تحميل الإضافة:
1. اذهب إلى `chrome://extensions/`
2. اضغط زر "إعادة تحميل" للإضافة

#### ب. اختبار الاستخراج:
1. اذهب إلى نور: `https://noor.moe.gov.sa/Noor/UsersManagement/UserSearch.aspx`
2. قم بإعداد البحث واضغط "بحث"
3. افتح Developer Tools (F12) وراقب Console

**الرسائل المتوقعة:**
```
📦 إضافة نور: بدء مراقبة الصفحة بعد الضغط على بحث...
🔍 تشخيص الصفحة:
📋 عدد الجداول: X
✅ تم العثور على الجدول: GridClass
📄 أعلى رقم صفحة تم العثور عليه: X
✅ تم العثور على الجدول مع X صف إجمالي، X صف بيانات
📝 طالب 1: ابراهيم تركي ابراهيم الشدوخي - ID: 1158167138
📝 طالب 2: ابراهيم علاء بركات العبيد - ID: 2352078659
...
✅ تم استخراج X طالبًا من الصفحة 1
📄 الانتقال إلى الصفحة 2...
```

#### ج. اختبار popup:
1. بعد انتهاء الاستخراج، افتح popup الإضافة
2. يجب أن تظهر البيانات مع الأعمدة الجديدة
3. جرب الأزرار:
   - 🔄 تحديث البيانات
   - 🔍 فحص التخزين
   - 📥 تصدير Excel
   - 🚀 إرسال إلى السيرفر (محاكاة)

## 🔍 تشخيص المشاكل

### إذا لم يتم العثور على الجدول:
```javascript
// في console المتصفح، جرب:
document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo")
document.querySelector("table.GridClass")
document.querySelectorAll("table") // لرؤية جميع الجداول
```

### إذا لم تظهر البيانات في popup:
```javascript
// في console popup، جرب:
chrome.storage.local.get(null, console.log)
```

### إذا فشل التنقل بين الصفحات:
- تحقق من وجود روابط `Page$2`, `Page$3` في صف `MyPagerStyle`
- تحقق من وجود `__doPostBack` function

## 📊 البيانات المستخرجة

الآن يتم استخراج البيانات التالية لكل طالب:

```javascript
{
  username: "eb1122",                           // اسم المستخدم
  name: "ابراهيم تركي ابراهيم الشدوخي",        // الاسم الرباعي
  name_english: "IBRAHIM TURKI IBRAHIM...",     // الاسم بالإنجليزية
  national_id: "1158167138",                    // رقم السجل المدني
  study_system: "منتظم",                       // النظام الدراسي
  grade: "الثاني المتوسط",                     // الصف
  department: "قسم عام",                       // القسم
  class: "1",                                   // الفصل
  enrollment_status: "مرفع",                   // حالة القيد
  verification: "مدقق",                        // التدقيق
  record_status: "مستمر في الدراسة"            // حالة السجل
}
```

## ✅ قائمة التحقق

- [ ] test-table.html يعمل ويستخرج 3 طلاب
- [ ] الإضافة تجد الجدول في نور
- [ ] يتم استخراج البيانات من الصفحة الأولى
- [ ] يتم التنقل إلى الصفحات التالية
- [ ] popup يعرض البيانات بالأعمدة الجديدة
- [ ] زر "فحص التخزين" يعمل
- [ ] زر "تصدير Excel" يعمل
- [ ] زر "إرسال إلى السيرفر" يعمل (محاكاة)

## 🚨 مشاكل محتملة وحلولها

### المشكلة: "لم يظهر الجدول في الوقت المحدد"
**الحل:**
- تأكد من أن البحث في نور أظهر نتائج
- تحقق من أن الجدول يحتوي على بيانات حقيقية
- انتظر قليلاً قبل إعادة المحاولة

### المشكلة: "الأزرار لا تعمل في popup"
**الحل:**
- أعد تحميل الإضافة
- تحقق من console للأخطاء
- جرب زر "فحص التخزين" أولاً

### المشكلة: "البيانات لا تظهر في popup"
**الحل:**
- اضغط زر "تحديث البيانات"
- اضغط زر "فحص التخزين" لرؤية البيانات المحفوظة
- تحقق من console للأخطاء

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من console المتصفح (F12)
2. جرب test-table.html أولاً
3. تأكد من إعادة تحميل الإضافة
4. تحقق من أن نور يعرض الجدول بشكل صحيح
