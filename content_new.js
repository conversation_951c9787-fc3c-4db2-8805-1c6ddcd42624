// الحل الجذري الجديد لنظام نور
// متغيرات عامة
let allStudents = [];
let currentPage = 1;
let totalPages = 1;
let isExtracting = false;

// الحل الجذري للتنقل بين الصفحات
function goToPage(pageNumber) {
  console.log(`🚀 الحل الجذري: الانتقال للصفحة ${pageNumber}`);
  
  const pagerRow = document.querySelector("tr.MyPagerStyle");
  if (!pagerRow) {
    console.warn("❌ لم يتم العثور على صف التنقل");
    return false;
  }

  const allElements = pagerRow.querySelectorAll("a, span");
  console.log(`🔍 عناصر التنقل: ${allElements.length}`);

  // طباعة جميع العناصر للتشخيص
  allElements.forEach((element, index) => {
    const text = element.textContent.trim();
    const tag = element.tagName.toLowerCase();
    const href = element.getAttribute('href');
    console.log(`عنصر ${index + 1}: "${text}" - ${tag} - href: ${href}`);
  });

  // البحث عن رابط الصفحة المطلوبة
  const targetLink = Array.from(allElements).find(element => {
    const text = element.textContent.trim();
    const isLink = element.tagName.toLowerCase() === 'a';
    return text === pageNumber.toString() && isLink;
  });

  if (targetLink) {
    const href = targetLink.getAttribute('href');
    console.log(`✅ تم العثور على رابط الصفحة ${pageNumber}: ${href}`);
    
    try {
      // الطريقة الجذرية: تنفيذ JavaScript الموجود في href مباشرة
      if (href && href.startsWith('javascript:')) {
        const jsCode = href.replace('javascript:', '');
        console.log(`🔗 تنفيذ JavaScript: ${jsCode}`);
        
        // تنفيذ الكود مباشرة
        eval(jsCode);
        
        console.log(`✅ تم تنفيذ JavaScript للانتقال للصفحة ${pageNumber}`);
        return true;
      } else {
        // إذا لم يكن javascript، جرب click عادي
        targetLink.click();
        console.log(`🔗 تم الضغط على الرابط للصفحة ${pageNumber}`);
        return true;
      }
      
    } catch (error) {
      console.error(`❌ خطأ في تنفيذ الانتقال:`, error);
      return false;
    }
  } else {
    // التحقق من كوننا في الصفحة المطلوبة بالفعل
    const currentPageSpan = Array.from(allElements).find(element => {
      const text = element.textContent.trim();
      const isSpan = element.tagName.toLowerCase() === 'span';
      return text === pageNumber.toString() && isSpan;
    });
    
    if (currentPageSpan) {
      console.log(`✅ نحن بالفعل في الصفحة ${pageNumber}`);
      return true;
    }
    
    console.warn(`❌ لم يتم العثور على رابط الصفحة ${pageNumber}`);
    return false;
  }
}

// الحل الجذري لجلب تفاصيل الطالب
async function getStudentDetails(username, linkInfo) {
  return new Promise((resolve) => {
    console.log(`🔍 جلب تفاصيل الطالب: ${username}`);
    
    const currentLink = document.querySelector(`a[id="${linkInfo.id}"]`) || 
                       Array.from(document.querySelectorAll('a')).find(a => a.textContent.trim() === username);
    
    if (!currentLink) {
      console.error(`❌ لم يتم العثور على رابط الطالب ${username}`);
      resolve({});
      return;
    }
    
    const href = currentLink.getAttribute('href') || linkInfo.href;
    console.log(`🔗 معالجة رابط الطالب: ${href}`);
    
    try {
      if (href && href.includes('WebForm_DoPostBackWithOptions')) {
        // استخراج target من WebForm_DoPostBackWithOptions
        const match = href.match(/WebForm_DoPostBackWithOptions\(new WebForm_PostBackOptions\("([^"]+)"/);
        if (match) {
          const target = match[1];
          console.log(`🔗 تنفيذ WebForm PostBack: target=${target}`);
          
          // تنفيذ JavaScript مباشرة
          const jsCode = href.replace('javascript:', '');
          eval(jsCode);
          
          setTimeout(() => {
            try {
              const details = extractDetailsFromPage();
              window.history.back();
              setTimeout(() => resolve(details), 2000);
            } catch (error) {
              console.error(`❌ خطأ في استخراج تفاصيل ${username}:`, error);
              window.history.back();
              setTimeout(() => resolve({}), 2000);
            }
          }, 3000);
        } else {
          console.error(`❌ لا يمكن استخراج target من: ${href}`);
          resolve({});
        }
      } else {
        console.error(`❌ نوع رابط غير مدعوم: ${href}`);
        resolve({});
      }
    } catch (error) {
      console.error(`❌ خطأ في معالجة الرابط:`, error);
      resolve({});
    }
  });
}

// استخراج التفاصيل من صفحة تفاصيل الطالب
function extractDetailsFromPage() {
  const details = {};
  
  const detailsTable = document.querySelector('table');
  if (!detailsTable) return details;
  
  const rows = detailsTable.querySelectorAll('tr');
  
  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length >= 2) {
      const label = cells[0]?.textContent?.trim();
      const value = cells[1]?.textContent?.trim();
      
      if (label && value) {
        const fieldMap = {
          'الاسم الكامل': 'full_name',
          'اسم المستخدم': 'username',
          'رقم الهوية': 'national_id',
          'تاريخ الميلاد': 'birth_date',
          'الإدارة التعليمية': 'education_department',
          'المدرسة': 'school',
          'الصف': 'grade',
          'الفصل': 'class',
          'حالة الطالب': 'student_status',
          'الجنسية': 'nationality',
          'هاتف المنزل': 'home_phone',
          'الجوال': 'mobile',
          'البريد الإلكتروني': 'email'
        };
        
        const fieldKey = fieldMap[label] || label.replace(/\s+/g, '_').toLowerCase();
        details[fieldKey] = value;
      }
    }
  });
  
  console.log(`✅ تم استخراج تفاصيل الطالب:`, details);
  return details;
}

// استخراج الطلاب من الجدول
function extractStudentsFromTable() {
  console.log(`🔍 استخراج البيانات من الصفحة ${currentPage}...`);

  // البحث عن الجدول بطرق متعددة
  let table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo");
  if (!table) table = document.querySelector("table[id*='gvUserInfo']");
  if (!table) table = document.querySelector("table.GridClass");
  if (!table) {
    // البحث في جميع الجداول
    const allTables = document.querySelectorAll("table");
    console.log(`🔍 عدد الجداول في الصفحة: ${allTables.length}`);

    allTables.forEach((t, index) => {
      const id = t.id || 'بدون ID';
      const className = t.className || 'بدون class';
      const rows = t.querySelectorAll('tr').length;
      console.log(`جدول ${index + 1}: ID=${id}, Class=${className}, Rows=${rows}`);
    });

    // جرب أول جدول يحتوي على أكثر من 5 صفوف
    table = Array.from(allTables).find(t => t.querySelectorAll('tr').length > 5);
  }

  if (!table) {
    console.warn("❌ لم يتم العثور على جدول البيانات");
    return;
  }

  console.log(`✅ استخراج البيانات من الجدول في الصفحة ${currentPage}`);

  const rows = table.querySelectorAll("tr") || [];
  let count = 0;
  let pageStudents = [];

  rows.forEach((row, index) => {
    const cols = row.querySelectorAll("td");

    if (cols.length >= 8 && !row.classList.contains('MyPagerStyle')) {
      const usernameLink = cols[0].querySelector('a');
      const username = usernameLink ? usernameLink.textContent.trim() : cols[0].textContent.trim();
      
      const studentData = {
        username,
        name: cols[1]?.textContent.trim() || '',
        name_english: cols[2]?.textContent.trim() || '',
        national_id: cols[3]?.textContent.trim() || '',
        study_system: cols[4]?.textContent.trim() || '',
        grade: cols[5]?.textContent.trim() || '',
        department: cols[6]?.textContent.trim() || '',
        class: cols[7]?.textContent.trim() || '',
        enrollment_status: cols[8]?.textContent.trim() || '',
        verification: cols[9]?.textContent.trim() || '',
        record_status: cols[10]?.textContent.trim() || '',
        page: currentPage
      };

      if (studentData.name && studentData.name.length > 2 && studentData.national_id && studentData.national_id.length >= 8) {
        const isDuplicate = allStudents.some(existing => existing.username === studentData.username);

        if (!isDuplicate) {
          pageStudents.push(studentData);
          count++;
          console.log(`📝 طالب جديد ${allStudents.length + count}:`, studentData.name);
        }
      }
    }
  });

  allStudents.push(...pageStudents);
  console.log(`✅ تم استخراج ${count} طالبًا جديدًا من الصفحة ${currentPage} (إجمالي: ${allStudents.length})`);
}

// حساب إجمالي عدد الصفحات
function getTotalPages() {
  const pagerRow = document.querySelector("tr.MyPagerStyle");
  if (!pagerRow) return 1;

  const pageLinks = pagerRow.querySelectorAll("a[href*='Page$']");
  let maxPage = 1;

  pageLinks.forEach(link => {
    const href = link.getAttribute('href');
    const match = href.match(/Page\$(\d+)/);
    if (match) {
      const pageNum = parseInt(match[1]);
      if (pageNum > maxPage) maxPage = pageNum;
    }
  });

  console.log(`📄 إجمالي عدد الصفحات: ${maxPage}`);
  return maxPage;
}

// بدء الاستخراج
function startExtraction() {
  console.log("🚀 بدء الاستخراج الجذري الجديد");
  
  allStudents = [];
  currentPage = 1;
  isExtracting = true;
  
  totalPages = getTotalPages();
  extractAndContinue();
}

// استخراج ومتابعة
function extractAndContinue() {
  extractStudentsFromTable();
  
  if (currentPage < totalPages) {
    currentPage++;
    console.log(`📄 الانتقال للصفحة ${currentPage}...`);
    
    if (goToPage(currentPage)) {
      setTimeout(() => extractAndContinue(), 3000);
    } else {
      finishExtraction();
    }
  } else {
    finishExtraction();
  }
}

// إنهاء الاستخراج
function finishExtraction() {
  console.log(`✅ اكتمل استخراج ${allStudents.length} طالبًا من ${totalPages} صفحة`);
  
  chrome.storage.local.set({
    studentsData: allStudents,
    totalStudentsCount: allStudents.length
  }, () => {
    console.log(`💾 تم حفظ ${allStudents.length} طالب`);
    alert(`✅ تم استخراج ${allStudents.length} طالبًا بنجاح!`);
  });
  
  isExtracting = false;
}

// انتظار تحميل الجدول ثم البدء
function waitForTable() {
  const table = document.querySelector("#ctl00_PlaceHolderMain_gvUserInfo") ||
                document.querySelector("table[id*='gvUserInfo']") ||
                document.querySelector("table.GridClass");

  if (table) {
    console.log("✅ تم العثور على الجدول، بدء الاستخراج...");
    startExtraction();
  } else {
    console.log("⏳ انتظار تحميل الجدول...");
    setTimeout(waitForTable, 1000);
  }
}

// بدء التشغيل
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', waitForTable);
} else {
  waitForTable();
}
