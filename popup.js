// ملف JavaScript منفصل لـ popup.html

// تنسيق التاريخ
function formatDate(date) {
  return date.toLocaleString('ar-SA', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// حساب الإحصائيات
function calculateStats(studentsData) {
  if (!studentsData || studentsData.length === 0) {
    return {
      totalStudents: 0,
      totalGrades: 0,
      totalClasses: 0,
      lastUpdate: formatDate(new Date())
    };
  }

  const grades = [...new Set(studentsData.map(s => s.grade).filter(g => g))];
  const classes = [...new Set(studentsData.map(s => s.class).filter(c => c))];

  return {
    totalStudents: studentsData.length,
    totalGrades: grades.length,
    totalClasses: classes.length,
    lastUpdate: formatDate(new Date())
  };
}

// عرض البيانات
function displayStudentsData(studentsData) {
  const container = document.getElementById("studentsTable");
  const statsSection = document.getElementById("statsSection");

  console.log("🎨 عرض البيانات:", studentsData ? studentsData.length : 0, "طالب");

  if (!studentsData || studentsData.length === 0) {
    container.innerHTML = `
      <div class="no-data">
        <h4>📭 لا توجد بيانات</h4>
        <p>اذهب إلى نظام نور وأعد تحميل الصفحة</p>
        <p>الإضافة ستعمل تلقائياً وتستخرج البيانات</p>
      </div>
    `;
    statsSection.style.display = 'none';
    return;
  }

  // عرض الإحصائيات
  const stats = calculateStats(studentsData);
  document.getElementById("totalStudents").textContent = stats.totalStudents;
  document.getElementById("totalGrades").textContent = stats.totalGrades;
  document.getElementById("totalClasses").textContent = stats.totalClasses;
  document.getElementById("lastUpdate").textContent = stats.lastUpdate;
  statsSection.style.display = 'block';

  // عرض الجدول
  let html = `
    <table class="students-table">
      <thead>
        <tr>
          <th>اسم المستخدم</th>
          <th>الاسم</th>
          <th>الاسم بالإنجليزية</th>
          <th>رقم الهوية</th>
          <th>النظام الدراسي</th>
          <th>الصف</th>
          <th>القسم</th>
          <th>الفصل</th>
          <th>حالة القيد</th>
          <th>التدقيق</th>
          <th>حالة السجل</th>
        </tr>
      </thead>
      <tbody>
  `;

  studentsData.forEach((student, index) => {
    html += `
      <tr>
        <td>${student.username || ''}</td>
        <td>${student.name || ''}</td>
        <td>${student.name_english || ''}</td>
        <td>${student.national_id || ''}</td>
        <td>${student.study_system || ''}</td>
        <td>${student.grade || ''}</td>
        <td>${student.department || ''}</td>
        <td>${student.class || ''}</td>
        <td>${student.enrollment_status || ''}</td>
        <td>${student.verification || ''}</td>
        <td>${student.record_status || ''}</td>
      </tr>
    `;
  });

  html += `
      </tbody>
    </table>
  `;
  container.innerHTML = html;
}

// تحميل البيانات
function loadData() {
  chrome.storage.local.get(null, (result) => {
    console.log("📦 البيانات المحفوظة:", result);

    // البحث عن البيانات بطريقة مباشرة
    let studentsData = null;
    
    if (result.studentsData && Array.isArray(result.studentsData)) {
      studentsData = result.studentsData;
      console.log("✅ وجدت البيانات في studentsData:", studentsData.length);
    } else if (result.allStudents && Array.isArray(result.allStudents)) {
      studentsData = result.allStudents;
      console.log("✅ وجدت البيانات في allStudents:", studentsData.length);
    } else if (result.noorStudentsData && result.noorStudentsData.studentsData) {
      studentsData = result.noorStudentsData.studentsData;
      console.log("✅ وجدت البيانات في noorStudentsData:", studentsData.length);
    }

    // عرض البيانات مباشرة
    if (studentsData && studentsData.length > 0) {
      console.log("✅ عرض البيانات:", studentsData.length, "طالب");
      displayStudentsData(studentsData);
    } else {
      console.log("❌ لا توجد بيانات للعرض");
      displayStudentsData([]);
    }
  });
}

// تصدير البيانات إلى Excel
function exportToExcel(studentsData) {
  if (!studentsData || studentsData.length === 0) {
    alert("❌ لا توجد بيانات للتصدير");
    return;
  }

  let csvContent = "اسم المستخدم,الاسم,الاسم بالإنجليزية,رقم الهوية,النظام الدراسي,الصف,القسم,الفصل,حالة القيد,التدقيق,حالة السجل\n";
  studentsData.forEach(s => {
    csvContent += `"${s.username || ''}","${s.name || ''}","${s.name_english || ''}","${s.national_id || ''}","${s.study_system || ''}","${s.grade || ''}","${s.department || ''}","${s.class || ''}","${s.enrollment_status || ''}","${s.verification || ''}","${s.record_status || ''}"\n`;
  });

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute("download", `noor_students_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  alert(`✅ تم تصدير ${studentsData.length} طالب بنجاح!`);
}

// الأحداث
document.addEventListener('DOMContentLoaded', () => {
  loadData();
  
  // تحديث كل 3 ثوان
  setInterval(loadData, 3000);

  // زر التحديث
  document.getElementById("refreshBtn").addEventListener("click", loadData);

  // زر فحص التخزين
  document.getElementById("debugBtn").addEventListener("click", () => {
    chrome.storage.local.get(null, (result) => {
      let info = "البيانات المحفوظة:\n\n";
      for (const key in result) {
        if (Array.isArray(result[key])) {
          info += `${key}: ${result[key].length} عنصر\n`;
        } else {
          info += `${key}: ${typeof result[key]}\n`;
        }
      }
      alert(info || "لا توجد بيانات");
    });
  });

  // زر التصدير
  document.getElementById("exportBtn").addEventListener("click", () => {
    chrome.storage.local.get(null, (result) => {
      let studentsData = result.studentsData || result.allStudents;
      if (!studentsData && result.noorStudentsData) {
        studentsData = result.noorStudentsData.studentsData;
      }
      exportToExcel(studentsData);
    });
  });

  // زر الإرسال
  document.getElementById("sendBtn").addEventListener("click", () => {
    chrome.storage.local.get(null, (result) => {
      let studentsData = result.studentsData || result.allStudents;
      if (!studentsData && result.noorStudentsData) {
        studentsData = result.noorStudentsData.studentsData;
      }
      
      if (!studentsData || studentsData.length === 0) {
        alert("❌ لا توجد بيانات لإرسالها");
        return;
      }

      const sendBtn = document.getElementById("sendBtn");
      sendBtn.disabled = true;
      sendBtn.textContent = "🔄 جاري الإرسال...";

      // محاكاة الإرسال
      setTimeout(() => {
        alert("✅ تم إرسال البيانات بنجاح (محاكاة)");
        sendBtn.disabled = false;
        sendBtn.textContent = "🚀 إرسال إلى السيرفر";
      }, 2000);
    });
  });
});
