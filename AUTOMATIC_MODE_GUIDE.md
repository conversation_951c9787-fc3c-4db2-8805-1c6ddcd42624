# دليل الوضع التلقائي - إضافة جلب طلاب نور

## 🎯 التحديث الجديد: الوضع التلقائي الكامل

تم تحديث الإضافة لتعمل **تلقائياً بالكامل** بدون أي تدخل من المستخدم!

## 🚀 كيف تعمل الإضافة الآن

### 1. عند تحميل الصفحة:
```
🚀 بدء تشغيل إضافة نور...
📊 حالة الصفحة: search_ready
🔍 الصفحة جاهزة للبحث، بدء المراقبة التلقائية...
📢 إضافة نور تعمل في الخلفية وستبدأ تلقائياً عند ظهور النتائج
```

### 2. المراقبة التلقائية:
- **مراقبة زر البحث**: كشف الضغط على أي زر بحث
- **مراقبة تغييرات الصفحة**: فحص كل 3 ثوان للتغييرات
- **مراقبة DOM**: كشف إضافة جداول جديدة
- **فحص ذكي**: تحليل محتوى الجداول للبحث عن بيانات طلاب

### 3. عند ظهور النتائج:
```
🔄 فحص تلقائي 5/30 - الحالة: data_ready
✅ تم اكتشاف تغيير الحالة إلى data_ready، بدء الاستخراج...
📝 طالب 1: أحمد محمد علي - ID: 1234567890
```

## 📋 خطوات الاستخدام المبسطة

### 1. إعداد الإضافة (مرة واحدة):
1. **أعد تحميل الإضافة** في `chrome://extensions/`
2. تأكد من تفعيلها

### 2. الاستخدام اليومي:
1. **اذهب إلى صفحة البحث في نور**
2. **أعد تحميل الصفحة** (F5)
3. **اختر معايير البحث** (المدرسة، الصف، إلخ)
4. **اضغط زر "بحث"**
5. **انتظر** - الإضافة ستعمل تلقائياً!

## 🔍 ما تراقبه الإضافة

### أ. كشف بيانات الطلاب:
```javascript
// البحث عن أنماط بيانات الطلاب:
- أرقام هوية (8 أرقام أو أكثر): /^\d{8,}$/
- أسماء صفوف: /الصف|المتوسط|الثانوي|الابتدائي/
- أسماء مستخدمين: /^[a-zA-Z0-9]+$/
- أسماء عربية: /[\u0600-\u06FF]/
```

### ب. مراقبة التغييرات:
- **كل 3 ثوان**: فحص حالة الصفحة
- **30 محاولة**: إجمالي 90 ثانية مراقبة
- **كشف فوري**: للجداول الجديدة عبر DOM Observer

### ج. حالات الصفحة:
- **search_ready**: جاهزة للبحث
- **data_ready**: البيانات موجودة
- **error**: رسالة خطأ
- **unknown**: حالة غير واضحة

## 📊 رسائل المراقبة

### رسائل عادية (لا تحتاج تدخل):
```
🔄 فحص تلقائي 1/30 - الحالة: search_ready
🔄 فحص تلقائي 2/30 - الحالة: search_ready
🔄 فحص تلقائي 3/30 - الحالة: search_ready
```

### رسائل النجاح:
```
✅ تم اكتشاف تغيير الحالة إلى data_ready، بدء الاستخراج...
✅ تم العثور على جدول يحتوي على بيانات طلاب
📝 طالب 1: ابراهيم تركي ابراهيم الشدوخي - ID: 1158167138
```

### رسائل التحذير:
```
⏰ انتهت مهلة المراقبة التلقائية
❌ تم اكتشاف رسالة خطأ جديدة
```

## 🎯 مزايا الوضع التلقائي

### ✅ لا يحتاج تدخل من المستخدم
- تعمل في الخلفية
- تكشف التغييرات تلقائياً
- تبدأ الاستخراج فوراً

### ✅ مراقبة شاملة
- مراقبة زر البحث
- مراقبة تغييرات الصفحة
- مراقبة DOM للجداول الجديدة

### ✅ كشف ذكي للبيانات
- تحليل محتوى الجداول
- البحث عن أنماط بيانات الطلاب
- تجاهل الجداول الفارغة

### ✅ مرونة في التعامل
- يعمل مع جداول مختلفة
- يتكيف مع تغييرات نور
- يعمل مع سرعات إنترنت مختلفة

## ⚠️ نصائح مهمة

### 1. اتبع الترتيب:
1. أعد تحميل الصفحة أولاً
2. انتظر رسالة "بدء المراقبة التلقائية"
3. اختر معايير البحث
4. اضغط "بحث"

### 2. راقب Console:
- اضغط F12 لمراقبة العملية
- ابحث عن رسائل "✅" للنجاح
- ابحث عن رسائل "❌" للمشاكل

### 3. كن صبوراً:
- الإضافة تراقب لمدة 90 ثانية
- بعض البحوث تستغرق وقت أطول
- لا تغلق النافذة أثناء المراقبة

## 🔧 استكشاف الأخطاء

### إذا لم تبدأ المراقبة:
1. أعد تحميل الإضافة
2. أعد تحميل الصفحة
3. تحقق من console للأخطاء

### إذا لم تكتشف النتائج:
1. تأكد من ظهور جدول بالبيانات
2. تحقق من أن البحث أرجع نتائج
3. انتظر وقت أطول (قد يستغرق دقيقة)

### إذا توقفت المراقبة:
1. ابحث عن رسالة "⏰ انتهت مهلة المراقبة"
2. أعد تحميل الصفحة وحاول مرة أخرى
3. تحقق من سرعة الإنترنت

## 🎉 النتيجة النهائية

الآن الإضافة:
- ✅ **تعمل تلقائياً 100%**
- ✅ **لا تحتاج أي تدخل من المستخدم**
- ✅ **تراقب جميع التغييرات**
- ✅ **تكشف البيانات ذكياً**
- ✅ **تبدأ الاستخراج فوراً**

**ما عليك سوى**:
1. إعادة تحميل الصفحة
2. إجراء البحث في نور
3. الانتظار - الإضافة ستقوم بالباقي!

---

**🚀 استمتع بالاستخراج التلقائي الكامل!**
